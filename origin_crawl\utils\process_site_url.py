from urllib.parse import urlparse
import logging

logger = logging.getLogger(__name__)

def process_url_data(urls_content):
    """处理URL数据，确保安全处理"""
    result = {}
    try:
        for url, content in urls_content.items():
            # 跳过None值或空字典
            if content is None or (isinstance(content, dict) and not content):
                continue
                
            # 确保URL是字符串
            url_str = str(url)
            
            # 处理不同类型的内容
            if isinstance(content, dict):
                # 复制字典避免修改原始数据
                content_copy = content.copy()
                result[url_str] = content_copy
            else:
                # 非字典类型转换为字符串
                result[url_str] = {"content": str(content)}
                
        return result
    except Exception as e:
        # 如果处理过程中发生错误，记录日志并返回安全的结果
        logger.error(f"处理URL数据时出错: {str(e)}")
        # 返回原始输入的安全版本
        safe_result = {}
        for url, content in urls_content.items():
            try:
                url_str = str(url)
                if content is None:
                    safe_result[url_str] = {"status": "missing"}
                elif isinstance(content, dict):
                    safe_result[url_str] = {"status": "dict", "length": len(content)}
                else:
                    safe_result[url_str] = {"status": "other", "type": str(type(content))}
            except:
                pass
        return safe_result
