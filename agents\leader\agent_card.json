{"name": "InformationLeaderAgent", "version": "1.0.0", "description": "Information collection group leader managing crawling and enrichment employees", "endpoint": "http://info-leader:8231/a2a", "capabilities": [{"name": "coordinate_information_collection", "description": "Coordinate website crawling and information enrichment tasks", "inputs": [{"type": "text", "name": "target_url"}, {"type": "text", "name": "company_name"}, {"type": "array", "name": "priority_keywords"}], "outputs": [{"type": "object", "name": "enriched_information"}]}, {"name": "manage_crawling_tasks", "description": "Manage and coordinate web crawling tasks across employees", "inputs": [{"type": "text", "name": "base_url"}, {"type": "integer", "name": "max_pages", "optional": true}, {"type": "integer", "name": "max_depth", "optional": true}], "outputs": [{"type": "object", "name": "crawling_results"}]}], "skills": ["task-coordination", "employee-management", "information-integration"], "supports": {"streaming": true, "pushNotifications": true, "crossGroupCalls": true}, "auth": ["none"], "protocolVersion": "1.0", "group": "information_collection", "employees": ["Crawl4AIEmployee", "ExaSearchEmployee"]}