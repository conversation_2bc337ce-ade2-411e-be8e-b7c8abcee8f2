#!/usr/bin/env python3
"""
启动Crawl4AI Employee的简化脚本
"""
import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse
    import uvicorn
    
    print("✅ FastAPI导入成功")
except ImportError as e:
    print(f"❌ FastAPI导入失败: {e}")
    sys.exit(1)

try:
    from common.models import Crawl4AIConfig, ServerConfig
    print("✅ 公共模型导入成功")
except ImportError as e:
    print(f"❌ 公共模型导入失败: {e}")
    sys.exit(1)

# 创建一个简化的测试应用
app = FastAPI(
    title="Crawl4AI Employee Agent (Test)",
    description="A2A-compatible web crawling agent using Crawl4AI",
    version="1.0.0"
)

@app.get("/")
async def root():
    return {"message": "Crawl4AI Employee Agent is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "agent": "Crawl4AI Employee"}

@app.get("/.well-known/agent.json")
async def get_agent_card():
    """返回简化的Agent Card"""
    return {
        "name": "Crawl4AIEmployee",
        "version": "1.0.0",
        "description": "Advanced web crawler using Crawl4AI for structured content extraction",
        "endpoint": "http://localhost:8232/a2a",
        "capabilities": [
            {
                "name": "crawl_website",
                "description": "Extract structured content from websites",
                "inputs": [{"type": "text", "name": "url"}],
                "outputs": [{"type": "object", "name": "structured_content"}]
            }
        ],
        "skills": ["web-crawling", "content-extraction"],
        "supports": {"streaming": True, "pushNotifications": False},
        "auth": ["none"],
        "protocolVersion": "1.0",
        "group": "information_collection"
    }

@app.post("/a2a")
async def handle_a2a_request():
    """简化的A2A端点"""
    return {
        "jsonrpc": "2.0",
        "id": "test",
        "result": {
            "id": "test-task",
            "status": {"state": "completed"},
            "artifacts": [
                {
                    "name": "test_result",
                    "parts": [{"type": "text", "text": "Test A2A response"}]
                }
            ]
        }
    }

if __name__ == "__main__":
    print("🚀 启动Crawl4AI Employee测试服务...")
    print("📍 服务地址: http://localhost:8232")
    print("📋 Agent Card: http://localhost:8232/.well-known/agent.json")
    print("🔗 A2A端点: http://localhost:8232/a2a")
    print("📖 API文档: http://localhost:8232/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8232,
        log_level="info"
    )
