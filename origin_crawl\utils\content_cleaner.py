"""
内容清洗模块，用于识别和移除网页中的公共部分（如导航栏、页脚等）
"""
import logging
from typing import List, Dict, Set, Optional, Tuple
import re
from bs4 import BeautifulSoup, Comment, NavigableString
# 导入markdownify库
from markdownify import markdownify as md
from multiprocessing import Pool
import hashlib
from lxml import html, etree

logger = logging.getLogger(__name__)

class ContentCleaner:
    """内容清洗器，用于识别和移除网页中的公共部分"""
    
    def __init__(self):
        """初始化内容清洗器"""
        # 存储各域名的首页内容
        
    
    def clean_html_content(self, html_content: str) -> str:
        """使用BeautifulSoup清理HTML内容，彻底移除无用标签和内容
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            清理后的HTML内容
        """
        if not html_content:
            return html_content
            
        soup = BeautifulSoup(html_content, 'lxml')
        removed_count = 0
        
        # 1. 移除所有注释
        comments = soup.find_all(string=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
            removed_count += 1
        logger.debug(f"移除HTML注释: {len(comments)}个")
        
        # 2. 移除页面框架相关的标签
        framework_tags = ['header', 'footer', 'nav', 'sidebar', 'aside', 'menu']
        for tag_name in framework_tags:
            for element in soup.find_all(tag_name):
                element.decompose()
                removed_count += 1
        
        def _remove_element_by_class(soup, class_name_list):
            _remove_element_count = 0
            for class_name in class_name_list:
                elements = soup.find_all('div', class_=class_name)
                for element in elements:
                    element.decompose()
                    _remove_element_count += 1
            return _remove_element_count
        # 3. 移除lz-mask-all div及其子元素,for daikin
        i = _remove_element_by_class(soup, ['lz-mask-all', 'footer-pc','rightFix','pc-dialog-info','footer-mb','foot-bottm','head','mask-all','media-pc','videoBanner','dialog-box-content','lz-right','reportPriceMode','reportPriceMode-list','dialogue-pc','media-mobile','dialogue-mobile','alert '])
        removed_count += i
        # 4. 处理不需要的标签，但保留其中的有用文本内容
        unnecessary_tags = [
            'script', 'style', 'iframe', 'noscript', 'embed', 'object',
            'link', 'meta', 'img', 'svg', 'canvas', 'button', 'select', 'option',
            'form', 'input', 'textarea', 'fieldset', 'hr', 'a', 'ul', 'li', 'i',
            'video', 'audio', 'source', 'track', 'picture', 'figure', 'figcaption',
            'map', 'area', 'use', 'sly'
        ]

        # 分为两组处理：
        # 1. 肯定不包含有用内容的标签（可以直接删除）
        direct_remove_tags = [
            'script', 'style', 'iframe', 'noscript', 'embed', 'object',
            'link', 'meta', 'canvas', 'input', 'textarea', 'hr', 'source', 'track',
            'map', 'area', 'use', 'sly'
        ]

        # 2. 可能包含有用内容的标签（需要保留内容）
        content_preserving_tags = [tag for tag in unnecessary_tags if tag not in direct_remove_tags]

        # 直接删除肯定没有有用内容的标签
        for tag_name in direct_remove_tags:
            for element in soup.find_all(tag_name):
                element.decompose()
                removed_count += 1

        # 处理可能包含有用内容的标签
        for tag_name in content_preserving_tags:
            for element in soup.find_all(tag_name):
                # 获取标签中的文本内容（去除空白）
                content = element.get_text(strip=True)
                
                # 如果有有意义的文本内容
                if content and len(content) > 2:  # 至少包含3个字符才认为有意义
                    # 创建一个新的span元素来保存内容
                    new_element = soup.new_tag('span')
                    new_element.string = content
                    
                    # 用新元素替换原标签
                    element.replace_with(new_element)
                else:
                    # 如果没有有意义的内容，直接删除
                    element.decompose()
                
                removed_count += 1
        
        # 5. 移除空的title标签
        for title in soup.find_all('title'):
            if not title.get_text(strip=True):
                title.decompose()
                removed_count += 1
        
        # 6. 移除无内容的头部标签（h1-h6）
        for h_tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            for element in soup.find_all(h_tag):
                if not element.get_text(strip=True):
                    element.decompose()
                    removed_count += 1
        
        # 7. 移除不必要的属性
        unwanted_attrs = ['class', 'id', 'style', 'data-cmp-data-layer-enabled', 
                          'data-is-wcmmode', 'aria-hidden', 'onclick', 'onload', 
                          'onmouseover', 'onmouseout', 'data-sly-resource',
                          'data-sly-include', 'data-sly-test']
        
        for tag in soup.find_all(True):
            for attr in list(tag.attrs):
                if attr in unwanted_attrs:
                    del tag[attr]
                # 移除所有data-*属性
                elif attr.startswith('data-'):
                    del tag[attr]
        
        # 8. 递归清理空标签 (最多执行5次清理循环)
        for clean_round in range(5):
            empty_tags_removed = 0
            # 移除所有空的非文本标签
            for element in soup.find_all():
                # 如果元素没有子元素，且没有文本内容
                if (not element.contents or 
                    all(isinstance(c, NavigableString) and not c.strip() for c in element.contents)) and \
                   not element.get_text(strip=True):
                    # 保留基本结构标签
                    if element.name not in ['html', 'body']:
                        element.decompose()
                        empty_tags_removed += 1
            
            # 如果没有移除任何标签，则结束循环
            if empty_tags_removed == 0:
                break
                
            removed_count += empty_tags_removed
            logger.debug(f"移除空标签 (第{clean_round+1}轮): {empty_tags_removed}个")
        
        # 9. 扁平化嵌套的div（如果一个div只有一个div子元素，并且没有文本内容）
        flatten_count = 0
        for _ in range(3):  # 最多尝试3轮扁平化
            div_flattened = 0
            for div in soup.find_all('div'):
                # 检查是否只有一个div子元素
                children = [c for c in div.children if not (isinstance(c, NavigableString) and not c.strip())]
                if len(children) == 1 and children[0].name == 'div':
                    # 检查div本身是否有直接文本（不在子div中）
                    if not ''.join(t for t in div.strings if t.parent == div).strip():
                        div.replace_with(children[0])
                        div_flattened += 1
            
            flatten_count += div_flattened
            if div_flattened == 0:
                break
                
        logger.debug(f"扁平化嵌套div: {flatten_count}个")
        removed_count += flatten_count
        
        # 10. 清理头部，只保留title
        head = soup.find('head')
        if head:
            title = head.find('title')
            # 创建新的head标签
            new_head = soup.new_tag('head')
            # 如果有title，添加到新head
            if title:
                new_head.append(title)
            # 替换原head
            head.replace_with(new_head)
        
        # 11. 最后一轮：移除多余的嵌套结构和没有有用内容的容器
        useful_content = False
        body = soup.find('body')
        if body:
            # 先检查body是否有实际内容
            text_content = body.get_text(strip=True)
            if len(text_content) < 50:  # 如果内容太少，可能是无用页面
                # 查找所有包含实际文本的元素
                text_elements = [e for e in body.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']) 
                                if len(e.get_text(strip=True)) > 20]
                if not text_elements:
                    logger.warning(f"清理后的页面几乎没有有用内容，文本长度: {len(text_content)}")
                else:
                    useful_content = True
            else:
                useful_content = True
        
        if removed_count > 0:
            logger.info(f"已从HTML中移除 {removed_count} 个无用元素")
            
        # 如果清理后内容还是很大但没有有用文本，返回简化的版本
        if not useful_content and len(str(soup)) > 1000:
            # 提取所有文本并重建一个简单结构
            text = soup.get_text(separator='\n\n', strip=True)
            simplified_html = f"<html><head><title>{soup.title.string if soup.title else ''}</title></head><body><div>{text}</div></body></html>"
            return simplified_html
            
        return str(soup)
    
    def process_page_content(self, html_content: Optional[str] = None) -> Dict[str, str]:
        """处理页面内容，对首页保留完整内容，对子页面移除公共部分
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            处理后的Markdown内容
        """
            
        # 如果有HTML内容且已有首页HTML，先清理HTML再转换为Markdown
        cleaned_content = None
        if html_content:
            cleaned_html = self.clean_html_content(html_content)
            
            # 如果HTML清理结果与输入不同，需要重新生成Markdown

            # 尝试从清理后的HTML重新提取Markdown内容
            soup = BeautifulSoup(cleaned_html, 'lxml')
            cleaned_content = self._extract_markdown_from_soup(soup) 

        return {"cleaned_content": cleaned_content, "cleaned_html": cleaned_html}
    
    def _extract_markdown_from_soup(self, soup: BeautifulSoup) -> str:
        """从BeautifulSoup对象提取Markdown内容，使用markdownify库直接转换
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            Markdown格式的内容
        """

        # 直接将soup对象转换为markdown
        html_str = str(soup)
        markdown_content = md(html_str, heading_style="ATX", 
                            strip=["script", "style"], 
                            escape_asterisks=True, 
                            escape_underscores=True)
        
        markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)
        
        # 如果转换结果为空，尝试直接获取文本内容
        if not markdown_content.strip():
            markdown_content = soup.get_text(separator='\n\n', strip=True)
            
        return markdown_content

    def _extract_markdown_manually(self, soup: BeautifulSoup) -> str:
        """使用手动方式从BeautifulSoup对象提取Markdown内容（作为备选方法）
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            Markdown格式的内容
        """
        text = ""
        
        # 提取标题
        if soup.title:
            text += f"# {soup.title.string.strip()}\n\n"
        
        # 提取标题元素
        for tag, level in [('h1', 1), ('h2', 2), ('h3', 3), ('h4', 4), ('h5', 5)]:
            for element in soup.find_all(tag):
                element_text = element.get_text(strip=True)
                if element_text:
                    text += f"{'#' * (level + 1)} {element_text}\n\n"
        
        # 提取段落
        for p in soup.find_all('p'):
            p_text = p.get_text(strip=True)
            if p_text:
                text += f"{p_text}\n\n"
        
        # 提取表格
        for table in soup.find_all('table'):
            # 表头
            headers = []
            thead = table.find('thead')
            if thead:
                for th in thead.find_all('th'):
                    headers.append(th.get_text(strip=True))
            
            if not headers and table.find('tr'):
                # 尝试从第一行获取标题
                first_row = table.find('tr')
                for td in first_row.find_all('td'):
                    headers.append(td.get_text(strip=True))
            
            if headers:
                text += "| " + " | ".join(headers) + " |\n"
                text += "| " + " | ".join(["---"] * len(headers)) + " |\n"
                
                # 表格内容
                tbody = table.find('tbody')
                rows = tbody.find_all('tr') if tbody else table.find_all('tr')[1:] if headers else table.find_all('tr')
                
                for tr in rows:
                    row_data = [td.get_text(strip=True) for td in tr.find_all('td')]
                    if row_data:
                        text += "| " + " | ".join(row_data) + " |\n"
                text += "\n"
        
        # 如果没有提取到结构化内容，返回整个文本
        if not text.strip():
            text = soup.get_text(separator='\n\n', strip=True)
            
        return text
    
    def get_element_hash(self,element):
        """
        计算元素的哈希值，用于快速比对
        """
        # 移除空白字符后计算哈希
        content = re.sub(r'\s+', '', str(element))
        return hashlib.md5(content.encode()).hexdigest()

    def clean_child_html_content(self,args):
        """
        清洗HTML内容，移除与父级相同的部分
        """
        parent_html, child_html = args

        # 使用lxml解析器替代BeautifulSoup，性能更好
        parent_tree = html.fromstring(parent_html)
        child_tree = html.fromstring(child_html)

        # 预处理父级元素，建立哈希表
        parent_hashes = set()
        for parent_element in parent_tree.iter():
            if isinstance(parent_element.tag, str):  # 排除注释等特殊节点
                parent_hashes.add(self.get_element_hash(etree.tostring(parent_element)))

        # 需要删除的元素
        elements_to_remove = []

        # 遍历子元素
        for child_element in child_tree.iter():
            if not isinstance(child_element.tag, str):
                continue

            child_hash = self.get_element_hash(etree.tostring(child_element))

            # 使用哈希值快速比对
            if child_hash in parent_hashes:
                elements_to_remove.append(child_element)

        # 从下往上删除元素，避免影响树结构
        for element in reversed(elements_to_remove):
            parent = element.getparent()
            if parent is not None:
                parent.remove(element)

        return etree.tostring(child_tree, encoding='unicode', pretty_print=True)

    def process_json_data(self,json_data, processes=10):
        """
        使用多进程处理JSON数据
        """
        parent_html = json_data.get('html', '')
        if not parent_html:
            return json_data

        # 准备并行处理的参数
        children_html = [(parent_html, child.get('html', '')) 
                        for child in json_data.get('children', [])
                        if child.get('html')]

        # 使用进程池并行处理
        with Pool(processes=processes) as pool:
            results = pool.map(self.clean_child_html_content, children_html)

        # 更新结果
        for child, result in zip(json_data['children'], results):
            child['html'] = result
            child['content'] = self._extract_markdown_from_soup(BeautifulSoup(result, 'lxml'))

        return json_data

