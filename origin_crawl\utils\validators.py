"""
验证工具模块，用于验证URL和爬取结果
"""
import re
from typing import List, Dict, Any, Tuple, Optional
from urllib.parse import urlparse
import tldextract

def is_valid_url(url: str) -> bool:
    """检查URL是否有效
    
    Args:
        url: 待验证的URL
        
    Returns:
        URL是否有效
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
    except:
        return False

def get_domain(url: str) -> str:
    """从URL中获取域名
    
    Args:
        url: URL
        
    Returns:
        域名
    """
    extracted = tldextract.extract(url)
    domain = f"{extracted.domain}.{extracted.suffix}"
    return domain

def is_valid_content(content: str) -> bool:
    """检查内容是否有效
    
    Args:
        content: 待验证的内容
        
    Returns:
        内容是否有效
    """
    # 检查内容是否为空
    if not content or len(content.strip()) == 0:
        return False
    
    # 检查内容是否过短（可能是错误页面）
    if len(content.strip()) < 50:
        return False
    
    return True

def detect_duplicate_content(content: str, content_db: List[str], threshold: float = 0.85) -> Tuple[bool, Optional[int]]:
    """检测重复内容
    
    通过简单的字符串比较来检测内容是否与已有内容重复
    
    Args:
        content: 待检测的内容
        content_db: 已有内容列表
        threshold: 重复阈值（0-1之间）
        
    Returns:
        (是否重复, 重复内容的索引)
    """
    # 使用简化的内容指纹进行比较
    content_fingerprint = content.strip().lower()
    # 移除所有空白字符以进行更严格的比较
    content_fingerprint = re.sub(r'\s+', '', content_fingerprint)
    
    for i, existing_content in enumerate(content_db):
        existing_fingerprint = existing_content.strip().lower()
        existing_fingerprint = re.sub(r'\s+', '', existing_fingerprint)
        
        # 计算简单的相似度
        if len(content_fingerprint) > 0 and len(existing_fingerprint) > 0:
            # 使用Jaccard相似度
            sim_score = calculate_jaccard_similarity(content_fingerprint, existing_fingerprint)
            if sim_score > threshold:
                return True, i
    
    return False, None

def calculate_jaccard_similarity(str1: str, str2: str, ngram_size: int = 3) -> float:
    """计算Jaccard相似度
    
    Args:
        str1: 第一个字符串
        str2: 第二个字符串
        ngram_size: n元组大小
        
    Returns:
        相似度（0-1之间）
    """
    # 生成n元组集合
    ngrams1 = set(str1[i:i+ngram_size] for i in range(len(str1) - ngram_size + 1))
    ngrams2 = set(str2[i:i+ngram_size] for i in range(len(str2) - ngram_size + 1))
    
    # 计算Jaccard相似度
    intersection = len(ngrams1.intersection(ngrams2))
    union = len(ngrams1.union(ngrams2))
    
    if union == 0:
        return 0.0
    
    return intersection / union 