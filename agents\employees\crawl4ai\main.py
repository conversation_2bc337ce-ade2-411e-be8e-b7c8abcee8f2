"""
Crawl4AI Employee Agent 主程序
"""
import asyncio
import logging
import json
from typing import Dict, Any
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from common.models import (
    TaskParams, TaskResult, CrawlOptions, SiteOptions, 
    Crawl4AIConfig, ServerConfig, Part, PartType
)
from common.a2a_protocol import A2AProtocolHandler, create_crawl4ai_agent_card
from .crawler import SmartCrawlerCore

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Crawl4AIEmployee:
    """Crawl4AI Employee Agent"""
    
    def __init__(self, config: Crawl4AIConfig, server_config: ServerConfig):
        self.config = config
        self.server_config = server_config
        
        # 创建Agent Card
        self.agent_card = create_crawl4ai_agent_card()
        self.agent_card.endpoint = f"http://localhost:{server_config.port}{server_config.a2a_endpoint}"
        
        # 创建A2A协议处理器
        self.protocol_handler = A2AProtocolHandler(self.agent_card)
        
        # 创建爬虫核心
        self.crawler_core = SmartCrawlerCore(config)
        
        # 注册任务处理器
        self._register_handlers()
        
        # 创建FastAPI应用
        self.app = self._create_app()
    
    def _register_handlers(self):
        """注册任务处理器"""
        self.protocol_handler.register_handler("crawl_website", self._handle_crawl_website)
        self.protocol_handler.register_handler("crawl_site", self._handle_crawl_site)
        self.protocol_handler.register_handler("default", self._handle_default)
    
    async def _handle_crawl_website(self, task_params: TaskParams) -> TaskResult:
        """处理网站爬取任务"""
        try:
            # 提取任务内容
            task_content = self.protocol_handler._extract_task_content(task_params.message)
            
            # 构建爬取选项
            url = task_content.get("target_url") or task_content.get("url")
            if not url:
                return self.protocol_handler.create_failure_result(
                    task_params.id, "Missing required parameter: url"
                )
            
            options = CrawlOptions(
                url=url,
                include_links=task_content.get("include_links", True),
                css_selectors=task_content.get("css_selectors"),
                timeout=task_content.get("timeout", self.config.timeout),
                extract_metadata=task_content.get("extract_metadata", True),
                clean_content=task_content.get("clean_content", True)
            )
            
            # 执行爬取
            result = await self.crawler_core.crawl_url(options)
            
            if result.success:
                # 创建成功的Artifact
                artifacts = [
                    self.protocol_handler.create_object_artifact(
                        "structured_content",
                        {
                            "url": result.url,
                            "title": result.metadata.get("title", "") if result.metadata else "",
                            "content": result.content,
                            "html": result.html,
                            "crawler_used": result.crawler_used
                        },
                        "Structured content extracted from the website"
                    )
                ]
                
                if result.links:
                    artifacts.append(
                        self.protocol_handler.create_object_artifact(
                            "extracted_links",
                            {"links": result.links},
                            "Links extracted from the website"
                        )
                    )
                
                if result.metadata:
                    artifacts.append(
                        self.protocol_handler.create_object_artifact(
                            "metadata",
                            result.metadata,
                            "Metadata extracted from the website"
                        )
                    )
                
                return self.protocol_handler.create_success_result(
                    task_params.id, artifacts, {"crawler_used": result.crawler_used}
                )
            else:
                return self.protocol_handler.create_failure_result(
                    task_params.id, f"Crawl failed: {result.error}"
                )
                
        except Exception as e:
            logger.error(f"Error in crawl_website: {e}")
            return self.protocol_handler.create_failure_result(
                task_params.id, f"Internal error: {str(e)}"
            )
    
    async def _handle_crawl_site(self, task_params: TaskParams) -> TaskResult:
        """处理网站批量爬取任务"""
        try:
            # 提取任务内容
            task_content = self.protocol_handler._extract_task_content(task_params.message)
            
            # 构建网站爬取选项
            base_url = task_content.get("base_url")
            if not base_url:
                return self.protocol_handler.create_failure_result(
                    task_params.id, "Missing required parameter: base_url"
                )
            
            options = SiteOptions(
                base_url=base_url,
                max_pages=task_content.get("max_pages", 50),
                max_depth=task_content.get("max_depth", 2),
                include_external=task_content.get("include_external", False),
                max_concurrent=task_content.get("max_concurrent", self.config.max_concurrent),
                priority_keywords=task_content.get("priority_keywords")
            )
            
            # 执行网站爬取
            result = await self.crawler_core.crawl_site(options)
            
            # 创建结果Artifact
            site_content_map = {}
            for url, crawl_result in result.results.items():
                site_content_map[url] = {
                    "success": crawl_result.success,
                    "content": crawl_result.content,
                    "title": crawl_result.metadata.get("title", "") if crawl_result.metadata else "",
                    "links_count": len(crawl_result.links) if crawl_result.links else 0,
                    "crawler_used": crawl_result.crawler_used
                }
            
            artifacts = [
                self.protocol_handler.create_object_artifact(
                    "site_content_map",
                    {
                        "base_url": result.base_url,
                        "total_pages": result.total_pages,
                        "successful_pages": result.successful_pages,
                        "failed_pages": result.failed_pages,
                        "content_map": site_content_map,
                        "summary": result.summary
                    },
                    f"Content map for {result.successful_pages} pages from {result.base_url}"
                )
            ]
            
            return self.protocol_handler.create_success_result(
                task_params.id, 
                artifacts, 
                {
                    "total_pages": result.total_pages,
                    "success_rate": result.successful_pages / result.total_pages if result.total_pages > 0 else 0
                }
            )
            
        except Exception as e:
            logger.error(f"Error in crawl_site: {e}")
            return self.protocol_handler.create_failure_result(
                task_params.id, f"Internal error: {str(e)}"
            )
    
    async def _handle_default(self, task_params: TaskParams) -> TaskResult:
        """默认任务处理器"""
        # 尝试从消息中提取URL进行简单爬取
        task_content = self.protocol_handler._extract_task_content(task_params.message)
        
        if "url" in task_content or "target_url" in task_content:
            return await self._handle_crawl_website(task_params)
        
        return self.protocol_handler.create_failure_result(
            task_params.id, "Unable to determine task type. Please provide 'url' or 'base_url' parameter."
        )
    
    def _create_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title="Crawl4AI Employee Agent",
            description="A2A-compatible web crawling agent using Crawl4AI",
            version="1.0.0"
        )
        
        # 添加CORS中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=self.server_config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Agent Card端点
        @app.get(self.server_config.agent_card_path)
        async def get_agent_card():
            return self.protocol_handler.get_agent_card()
        
        # A2A协议端点
        @app.post(self.server_config.a2a_endpoint)
        async def handle_a2a_request(request: Request):
            try:
                request_data = await request.json()
                response = await self.protocol_handler.handle_request(request_data)
                return JSONResponse(content=response)
            except Exception as e:
                logger.error(f"Error handling A2A request: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 健康检查端点
        @app.get("/health")
        async def health_check():
            return {"status": "healthy", "agent": "Crawl4AI Employee"}
        
        return app
    
    async def start(self):
        """启动Agent"""
        logger.info("Starting Crawl4AI Employee Agent...")
        await self.crawler_core.initialize()
        logger.info(f"Agent started on {self.server_config.host}:{self.server_config.port}")
    
    async def stop(self):
        """停止Agent"""
        logger.info("Stopping Crawl4AI Employee Agent...")
        await self.crawler_core.close()
        logger.info("Agent stopped")


async def main():
    """主函数"""
    # 配置
    crawl_config = Crawl4AIConfig(
        timeout=30,
        max_concurrent=5,
        enable_fallback=True,
        fallback_timeout=15,
        content_cleaning=True,
        extract_links=True
    )
    
    server_config = ServerConfig(
        host="0.0.0.0",
        port=8232,
        log_level="INFO"
    )
    
    # 创建Agent
    agent = Crawl4AIEmployee(crawl_config, server_config)
    
    # 启动Agent
    await agent.start()
    
    try:
        # 运行服务器
        config = uvicorn.Config(
            agent.app,
            host=server_config.host,
            port=server_config.port,
            log_level=server_config.log_level.lower()
        )
        server = uvicorn.Server(config)
        await server.serve()
    finally:
        await agent.stop()


if __name__ == "__main__":
    asyncio.run(main())
