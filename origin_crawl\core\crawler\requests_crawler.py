"""
基于requests的爬虫实现
"""
import asyncio
import logging
import requests
from typing import Dict, List, Any, Tuple
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import re

from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler, CrawlResult
from specific_multi_scraper.utils.validators import is_valid_url, get_domain
from specific_multi_scraper.utils.content_cleaner import ContentCleaner

# 配置日志
logger = logging.getLogger(__name__)

class RequestsCrawler(BaseCrawler):
    """基于requests和BeautifulSoup的爬虫实现"""

    def __init__(self, config: Dict[str, Any] = None, hooks=None):
        super().__init__(config, hooks)
        self.content_cleaner = ContentCleaner()
        self.session = None
        
    async def start(self):
        """初始化爬虫实例"""
        if self._crawler is None:
            # 创建requests会话
            self.session = requests.Session()
            
            # 添加默认请求头
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
            })
            
            self._crawler = True  # 标记已初始化
            logger.info("Requests爬虫已初始化")

    async def close(self):
        """关闭爬虫实例"""
        if self._crawler is not None:
            try:
                self.session.close()
                self.session = None
                self._crawler = None
                logger.info("Requests爬虫已关闭")
            except Exception as e:
                logger.error(f"关闭爬虫实例时出错: {str(e)}")
                self._crawler = None

    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """爬取单个URL"""
        # 调用父类方法，触发钩子
        return await super().crawl(url, **kwargs)
        
    async def _crawl_impl(self, url: str, **kwargs) -> CrawlResult:
        """实际的爬取实现"""
        if not is_valid_url(url):
            return CrawlResult.create_failure(
                url=url,
                error_type="InvalidURL",
                error_code="REQ001",
                description="无效的URL",
                crawler_used="requests"
            )
            
        try:
            # 如果爬虫没有初始化，先初始化
            if self._crawler is None:
                await self.start()
            
            timeout = self.config.get('timeout', 30)
            
            # 准备请求参数
            request_kwargs = {
                'timeout': timeout
            }
            
            if kwargs.get('headers'):
                request_kwargs['headers'] = kwargs.get('headers')
                
            if kwargs.get('cookies'):
                request_kwargs['cookies'] = kwargs.get('cookies')
                
            # 在单独的线程中运行同步请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: self.session.get(url, **request_kwargs)
            )
            
            # 检查响应状态
            if response.status_code != 200:
                return CrawlResult.create_failure(
                    url=url,
                    error_type="HTTPError",
                    error_code="REQ002",
                    description=f"HTTP错误: {response.status_code}",
                    crawler_used="requests"
                )
            
            # 保存原始HTML内容
            html_content = response.text
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'lxml')
            
            # 提取链接
            links = []
            if kwargs.get('extract_links', True):
                for anchor in soup.find_all('a', href=True):
                    href = anchor['href']
                    # 处理相对链接
                    abs_link = urljoin(url, href)
                    link_text = anchor.get_text(strip=True)
                    
                    links.append({
                        'href': abs_link,
                        'text': link_text
                    })
            
            
            # 创建结果对象
            result = CrawlResult.create_success(
                url=url,
                content="",
                html=html_content,  # 保存原始HTML
                format="markdown",
                crawler_used="requests"
            )
            
            # 添加链接和元数据
            result.links = links
            result.metadata = {
                'title': soup.title.string if soup.title else '',
                'status_code': response.status_code,
                'content_type': response.headers.get('Content-Type', '')
            }
            
            if result.is_success() and self.content_cleaner:
                # 处理内容
                result.raw_html = result.html
                cleaner_result = self.content_cleaner.process_page_content(
                    html_content=result.html
                )
                result.content = cleaner_result.get("cleaned_content")
                result.html = cleaner_result.get("cleaned_html")
            return result
                
        except asyncio.TimeoutError:
            return CrawlResult.create_failure(
                url=url,
                error_type="Timeout",
                error_code="REQ003",
                description="爬取超时",
                crawler_used="requests"
            )
        except Exception as e:
            logger.error(f"爬取异常: {str(e)}")
            return CrawlResult.create_failure(
                url=url,
                error_type="CrawlError",
                error_code="REQ004",
                description=str(e),
                crawler_used="requests"
            )

    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        max_concurrent = kwargs.get('max_concurrent', 
                                  self.config.get('max_concurrent_tasks', 10))
        base_url = kwargs.get('base_url')
            
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str) -> Tuple[str, CrawlResult]:
            async with semaphore:
                kwargs['is_homepage'] = (url == base_url) if base_url else False
                result = await self.crawl(url, **kwargs)
                return url, result
                
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = []
        
        for task in asyncio.as_completed(tasks):
            try:
                result = await task
                results.append(result)
            except Exception as e:
                logger.error(f"爬取任务异常: {str(e)}")
                
        return {url: result for url, result in results}
        
    async def crawl_site(self, base_url: str, **kwargs) -> Dict[str, CrawlResult]:
        """爬取整个网站 - 支持多层级递归爬取"""
        max_urls = kwargs.get('max_urls', 100)
        include_external = kwargs.get('include_external', False)
        max_concurrent = kwargs.get('max_concurrent', 
                                  self.config.get('max_concurrent_tasks', 10))
        max_depth = kwargs.get('max_depth', 1)  # 默认爬取深度为1
        
        # 初始化结果集和待爬取队列
        results = {}
        crawled_urls = set()
        to_crawl_queue = [(base_url, 0)]  # (url, depth)
        
        # 爬取首页
        result = await self.crawl(base_url, is_homepage=True, 
                                extract_links=True)
        results[base_url] = result
        crawled_urls.add(base_url)
        
        # 提取首页链接
        if result.is_success() and result.links:
            links = result.get_link_hrefs()
            base_domain = get_domain(base_url)
            
            # 过滤链接
            if not include_external:
                links = [link for link in links if get_domain(link) == base_domain]
            
            # on_collect_sub_url钩子函数
            links = await self.process_collected_urls(base_url, [link for link in links if link not in crawled_urls])
            
            # 添加第一层链接到待爬取队列
            for link in links:
                if link not in crawled_urls and len(to_crawl_queue) < max_urls:
                    to_crawl_queue.append((link, 1))
        
        # 广度优先爬取
        while to_crawl_queue and len(crawled_urls) < max_urls:
            # 获取当前批次要爬取的URL
            current_batch = []
            current_depths = {}
            
            while to_crawl_queue and len(current_batch) < max_concurrent:
                url, depth = to_crawl_queue.pop(0)
                if url not in crawled_urls:
                    current_batch.append(url)
                    current_depths[url] = depth
                    crawled_urls.add(url)
            
            if not current_batch:
                break
                
            # 并发爬取当前批次
            batch_results = await self.crawl_many(
                current_batch, 
                max_concurrent=max_concurrent,
                base_url=base_url,
                include_external=include_external,
                extract_links=True
            )
            
            # 更新结果集
            results.update(batch_results)
            
            # 处理当前批次中的链接，准备下一层爬取
            for url, result in batch_results.items():
                current_depth = current_depths.get(url, 0)
                
                # 如果达到最大深度，不再提取链接
                if current_depth >= max_depth:
                    continue
                    
                # 提取新的链接
                if result.is_success() and result.links:
                    new_links = result.get_link_hrefs()
                    
                    # 过滤链接
                    if not include_external:
                        new_links = [link for link in new_links if get_domain(link) == base_domain]
                        
                    # on_collect_sub_url钩子函数
                    new_links = await self.process_collected_urls(url, new_links)
                    
                    # 添加新链接到待爬取队列
                    for link in new_links:
                        if link not in crawled_urls and len(crawled_urls) + len(to_crawl_queue) < max_urls:
                            to_crawl_queue.append((link, current_depth + 1))
        return results