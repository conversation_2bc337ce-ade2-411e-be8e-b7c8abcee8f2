{"name": "Crawl4AIEmployee", "version": "1.0.0", "description": "Advanced web crawler using Crawl4AI for structured content extraction", "endpoint": "http://crawl4ai-employee:8232/a2a", "capabilities": [{"name": "crawl_website", "description": "Extract structured content from websites with intelligent parsing", "inputs": [{"type": "text", "name": "url"}, {"type": "boolean", "name": "include_links", "optional": true}, {"type": "array", "name": "css_selectors", "optional": true}], "outputs": [{"type": "object", "name": "structured_content"}, {"type": "array", "name": "extracted_links"}, {"type": "object", "name": "metadata"}]}, {"name": "crawl_site_batch", "description": "Batch crawl multiple URLs from a website", "inputs": [{"type": "text", "name": "base_url"}, {"type": "integer", "name": "max_pages", "optional": true}, {"type": "integer", "name": "max_depth", "optional": true}], "outputs": [{"type": "object", "name": "site_content_map"}]}], "skills": ["web-crawling", "content-extraction", "html-parsing", "javascript-rendering"], "supports": {"streaming": true, "pushNotifications": false, "fallbackMechanisms": ["playwright", "requests"]}, "auth": ["none"], "protocolVersion": "1.0", "group": "information_collection"}