"""
智能爬虫核心 - 基于Crawl4AI的简洁实现
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from urllib.parse import urljoin, urlparse
import aiohttp
import json

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.markdown_generation_strategy import Default<PERSON><PERSON>downGenerator
from playwright.async_api import async_playwright

from common.models import (
    CrawlOptions, SiteOptions, CrawlResult, SiteResult,
    StructuredData, CrawlError, Crawl4AIConfig
)

# 导入内容清洗器
from common.content_cleaner import ContentCleaner

logger = logging.getLogger(__name__)


class SmartCrawlerCore:
    """智能爬虫核心"""

    def __init__(self, config: Crawl4AIConfig):
        self.config = config
        self.crawler: Optional[AsyncWebCrawler] = None
        self._initialized = False
        # 初始化内容清洗器
        self.content_cleaner = ContentCleaner()
        
    async def initialize(self):
        """初始化爬虫"""
        if not self._initialized:
            self.crawler = AsyncWebCrawler()
            await self.crawler.start()
            self._initialized = True
            logger.info("Crawl4AI crawler initialized")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler and self._initialized:
            await self.crawler.close()
            self._initialized = False
            logger.info("Crawl4AI crawler closed")
    
    async def crawl_url(self, options: CrawlOptions) -> CrawlResult:
        """爬取单个URL"""
        await self.initialize()
        
        try:
            # 首先尝试Crawl4AI
            return await self._crawl_with_crawl4ai(options)
        except Exception as e:
            logger.warning(f"Crawl4AI failed for {options.url}: {e}")
            
            if self.config.enable_fallback:
                # 尝试降级方案
                return await self._intelligent_fallback(options, e)
            else:
                raise CrawlError(options.url, "crawl4ai_failed", str(e))
    
    async def crawl_site(self, options: SiteOptions) -> SiteResult:
        """爬取整个网站"""
        await self.initialize()
        
        results = {}
        crawled_urls = set()
        to_crawl = [(options.base_url, 0)]  # (url, depth)
        failed_count = 0
        
        # 获取域名用于过滤
        base_domain = urlparse(options.base_url).netloc
        
        while to_crawl and len(results) < options.max_pages:
            # 获取当前批次
            current_batch = []
            current_depths = {}
            
            while to_crawl and len(current_batch) < self.config.max_concurrent:
                url, depth = to_crawl.pop(0)
                if url not in crawled_urls:
                    current_batch.append(url)
                    current_depths[url] = depth
                    crawled_urls.add(url)
            
            if not current_batch:
                break
            
            # 并发爬取当前批次
            batch_results = await self._crawl_batch(current_batch)
            
            # 处理结果并提取新链接
            for url in current_batch:
                result = batch_results.get(url)
                if result and result.success:
                    results[url] = result
                    
                    # 如果未达到最大深度，提取链接
                    current_depth = current_depths.get(url, 0)
                    if current_depth < options.max_depth and result.links:
                        new_links = self._filter_links(
                            result.links, base_domain, options.include_external, crawled_urls
                        )
                        
                        # 添加新链接到待爬取队列
                        for link in new_links:
                            if len(results) + len(to_crawl) < options.max_pages:
                                to_crawl.append((link, current_depth + 1))
                else:
                    failed_count += 1
        
        return SiteResult(
            base_url=options.base_url,
            total_pages=len(results) + failed_count,
            successful_pages=len(results),
            failed_pages=failed_count,
            results=results,
            summary={
                "max_depth_reached": max(current_depths.values()) if current_depths else 0,
                "domains_crawled": list(set(urlparse(url).netloc for url in results.keys()))
            }
        )
    
    async def _crawl_with_crawl4ai(self, options: CrawlOptions) -> CrawlResult:
        """使用Crawl4AI主引擎爬取"""
        config = self._build_crawl4ai_config(options)
        
        try:
            result = await asyncio.wait_for(
                self.crawler.arun(url=options.url, config=config),
                timeout=options.timeout
            )
            
            return self._process_crawl4ai_result(options.url, result)
            
        except asyncio.TimeoutError:
            raise CrawlError(options.url, "timeout", f"Crawl4AI timeout after {options.timeout}s")
        except Exception as e:
            raise CrawlError(options.url, "crawl4ai_error", str(e))
    
    async def _intelligent_fallback(self, options: CrawlOptions, original_error: Exception) -> CrawlResult:
        """智能降级处理"""
        logger.info(f"Starting fallback for {options.url}")
        
        # 策略1: Playwright
        try:
            return await self._crawl_with_playwright(options)
        except Exception as e:
            logger.warning(f"Playwright fallback failed for {options.url}: {e}")
        
        # 策略2: aiohttp (最后手段)
        try:
            return await self._crawl_with_requests(options)
        except Exception as e:
            logger.error(f"All fallback methods failed for {options.url}: {e}")
            
            # 返回失败结果
            return CrawlResult(
                url=options.url,
                success=False,
                error=f"All methods failed. Original: {original_error}, Final: {e}",
                crawler_used="fallback_failed"
            )
    
    async def _crawl_with_playwright(self, options: CrawlOptions) -> CrawlResult:
        """Playwright备选方案"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            try:
                page = await browser.new_page()
                
                # 设置用户代理
                if self.config.user_agent:
                    await page.set_extra_http_headers({"User-Agent": self.config.user_agent})
                
                # 导航到页面
                await page.goto(options.url, timeout=self.config.fallback_timeout * 1000)
                await page.wait_for_load_state("networkidle")
                
                # 获取内容
                content = await page.content()
                title = await page.title()
                
                # 提取链接
                links = []
                if options.include_links:
                    link_elements = await page.query_selector_all("a[href]")
                    for element in link_elements:
                        href = await element.get_attribute("href")
                        text = await element.inner_text()
                        if href:
                            absolute_url = urljoin(options.url, href)
                            links.append({"href": absolute_url, "text": text.strip()})

                # 处理内容清洗
                processed_content = await page.inner_text("body") if content else ""
                processed_html = content

                if content and self.config.content_cleaning:
                    try:
                        # 使用内容清洗器处理HTML
                        cleaning_result = self.content_cleaner.process_page_content(content)
                        if cleaning_result and cleaning_result.get("cleaned_content"):
                            processed_content = cleaning_result["cleaned_content"]
                            processed_html = cleaning_result.get("cleaned_html", content)
                            logger.debug(f"Playwright: Successfully cleaned content for {options.url}")
                    except Exception as e:
                        logger.warning(f"Playwright: Content cleaning error for {options.url}: {e}")

                return CrawlResult(
                    url=options.url,
                    success=True,
                    content=processed_content,
                    html=processed_html,
                    links=links,
                    metadata={
                        "title": title,
                        "method": "playwright",
                        "content_cleaned": self.config.content_cleaning
                    },
                    crawler_used="playwright"
                )
                
            finally:
                await browser.close()
    
    async def _crawl_with_requests(self, options: CrawlOptions) -> CrawlResult:
        """aiohttp最后手段"""
        headers = {
            "User-Agent": self.config.user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(options.url, timeout=self.config.fallback_timeout) as response:
                if response.status != 200:
                    raise CrawlError(options.url, "http_error", f"HTTP {response.status}")
                
                content = await response.text()
                
                # 简单的内容提取
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(content, 'html.parser')

                # 提取文本内容
                text_content = soup.get_text(separator=' ', strip=True)

                # 提取链接
                links = []
                if options.include_links:
                    for a_tag in soup.find_all('a', href=True):
                        href = urljoin(options.url, a_tag['href'])
                        text = a_tag.get_text(strip=True)
                        links.append({"href": href, "text": text})

                # 处理内容清洗
                processed_content = text_content
                processed_html = content

                if content and self.config.content_cleaning:
                    try:
                        # 使用内容清洗器处理HTML
                        cleaning_result = self.content_cleaner.process_page_content(content)
                        if cleaning_result and cleaning_result.get("cleaned_content"):
                            processed_content = cleaning_result["cleaned_content"]
                            processed_html = cleaning_result.get("cleaned_html", content)
                            logger.debug(f"Requests: Successfully cleaned content for {options.url}")
                    except Exception as e:
                        logger.warning(f"Requests: Content cleaning error for {options.url}: {e}")

                return CrawlResult(
                    url=options.url,
                    success=True,
                    content=processed_content,
                    html=processed_html,
                    links=links,
                    metadata={
                        "title": soup.title.string if soup.title else "",
                        "method": "requests",
                        "content_cleaned": self.config.content_cleaning
                    },
                    crawler_used="requests"
                )
    
    async def _crawl_batch(self, urls: List[str]) -> Dict[str, CrawlResult]:
        """批量爬取URL"""
        semaphore = asyncio.Semaphore(self.config.max_concurrent)
        
        async def crawl_single(url: str) -> tuple[str, CrawlResult]:
            async with semaphore:
                try:
                    options = CrawlOptions(url=url, timeout=self.config.timeout)
                    result = await self.crawl_url(options)
                    return url, result
                except Exception as e:
                    logger.error(f"Failed to crawl {url}: {e}")
                    return url, CrawlResult(
                        url=url, success=False, error=str(e), crawler_used="batch_failed"
                    )
        
        tasks = [crawl_single(url) for url in urls]
        results = await asyncio.gather(*tasks)
        return dict(results)
    
    def _build_crawl4ai_config(self, options: CrawlOptions) -> CrawlerRunConfig:
        """构建Crawl4AI配置"""
        prune_filter = PruningContentFilter(
            threshold=5,
            threshold_type="dynamic",
            min_word_threshold=5
        )
        md_generator = DefaultMarkdownGenerator(content_filter=prune_filter)
        
        return CrawlerRunConfig(
            screenshot=False,
            magic=True,
            simulate_user=True,
            verbose=False,
            page_timeout=options.timeout * 1000,
            exclude_external_links=not options.include_links,
            word_count_threshold=10,
            markdown_generator=md_generator,
            wait_until="networkidle",
            delay_before_return_html=0.5
        )
    
    def _process_crawl4ai_result(self, url: str, result) -> CrawlResult:
        """处理Crawl4AI结果"""
        try:
            # 提取链接
            links = []
            if hasattr(result, 'links') and result.links:
                for link in result.links:
                    if isinstance(link, dict):
                        links.append(link)
                    else:
                        # 处理其他格式的链接
                        links.append({"href": str(link), "text": ""})

            # 获取原始HTML和Markdown内容
            raw_html = result.html if hasattr(result, 'html') else ""
            raw_markdown = result.markdown if hasattr(result, 'markdown') else ""

            # 使用内容清洗器处理HTML内容
            cleaned_content = ""
            cleaned_html = raw_html

            if raw_html and self.config.content_cleaning:
                try:
                    # 使用原有的内容清洗器
                    cleaning_result = self.content_cleaner.process_page_content(raw_html)
                    if cleaning_result and cleaning_result.get("cleaned_content"):
                        cleaned_content = cleaning_result["cleaned_content"]
                        cleaned_html = cleaning_result.get("cleaned_html", raw_html)
                        logger.debug(f"Successfully cleaned content for {url}")
                    else:
                        # 如果清洗失败，使用原始markdown
                        cleaned_content = raw_markdown
                        logger.warning(f"Content cleaning failed for {url}, using raw markdown")
                except Exception as e:
                    logger.warning(f"Content cleaning error for {url}: {e}, using raw markdown")
                    cleaned_content = raw_markdown
            else:
                # 如果不启用内容清洗，使用原始markdown
                cleaned_content = raw_markdown

            return CrawlResult(
                url=url,
                success=True,
                content=cleaned_content,
                html=cleaned_html,
                links=links,
                metadata={
                    "title": getattr(result, 'title', ''),
                    "description": getattr(result, 'description', ''),
                    "method": "crawl4ai",
                    "content_cleaned": self.config.content_cleaning,
                    "original_content_length": len(raw_html) if raw_html else 0,
                    "cleaned_content_length": len(cleaned_content) if cleaned_content else 0
                },
                crawler_used="crawl4ai"
            )

        except Exception as e:
            logger.error(f"Error processing Crawl4AI result for {url}: {e}")
            return CrawlResult(
                url=url,
                success=False,
                error=f"Result processing failed: {e}",
                crawler_used="crawl4ai"
            )
    
    def _filter_links(self, links: List[Dict], base_domain: str, include_external: bool, crawled_urls: Set[str]) -> List[str]:
        """过滤链接"""
        filtered = []
        
        for link in links:
            href = link.get("href", "")
            if not href or href in crawled_urls:
                continue
            
            # 解析URL
            parsed = urlparse(href)
            
            # 跳过非HTTP链接
            if parsed.scheme not in ["http", "https"]:
                continue
            
            # 域名过滤
            if not include_external and parsed.netloc != base_domain:
                continue
            
            # 跳过常见的非内容文件
            if href.lower().endswith(('.pdf', '.jpg', '.png', '.gif', '.mp4', '.zip', '.doc', '.docx')):
                continue
            
            filtered.append(href)
        
        return filtered
