"""
Playwright爬虫实现
"""
from playwright.async_api import async_playwright
import asyncio
import logging
from typing import Dict, List, Any, Tuple
import time

from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler, CrawlResult
from specific_multi_scraper.utils.validators import is_valid_url, get_domain
from specific_multi_scraper.utils.content_cleaner import ContentCleaner

# 配置日志
logger = logging.getLogger(__name__)

class PlaywrightCrawler(BaseCrawler):
    """基于Playwright的爬虫实现"""
    
    def __init__(self, config: Dict[str, Any] = None, hooks=None):
        super().__init__(config, hooks)
        self.content_cleaner = ContentCleaner()
        self._browser = None  # 确保这个属性在构造函数中初始化
        
    async def start(self):
        await super().start()
        
        if self._browser is None:
            import playwright.async_api as pw
            
            # 启动playwright
            playwright = await pw.async_playwright().start()
            
            # 确保浏览器以无头模式启动
            self._browser = await playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                ]
            )
            
            logger.info("Playwright浏览器已启动 (无头模式)")
            
            # 修改进程注册方式 - 不再尝试访问process属性
            # 而是记录一个标记，表示浏览器已启动
            try:
                # 记录浏览器启动时间作为标识
                browser_id = f"playwright_{time.time()}"
                self._browser_id = browser_id
                logger.info(f"注册Playwright浏览器实例ID: {browser_id}")
            except Exception as e:
                logger.warning(f"注册Playwright浏览器实例失败: {e}")
            
            # 创建新上下文
            self._browser_context = await self._browser.new_context(
                viewport={'width': 1280, 'height': 800}
            )

    async def close(self):
        # 先关闭上下文和浏览器
        if self._browser_context:
            try:
                await self._browser_context.close()
            except Exception as e:
                logger.debug(f"关闭Playwright浏览器上下文时出错: {e}")
            self._browser_context = None
        
        if self._browser:
            try:
                await self._browser.close()
            except Exception as e:
                logger.debug(f"关闭Playwright浏览器时出错: {e}")
            self._browser = None
        
        if hasattr(self, '_playwright') and self._playwright:
            try:
                await self._playwright.stop()
            except Exception as e:
                logger.debug(f"停止Playwright时出错: {e}")
            self._playwright = None
        
        # 释放浏览器引用
        await super().close()

    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """爬取单个URL"""
        # 调用父类方法，触发钩子
        return await super().crawl(url, **kwargs)
        
    async def _crawl_impl(self, url: str, **kwargs) -> CrawlResult:
        """实际的爬取实现"""
        if not is_valid_url(url):
            return CrawlResult.create_failure(
                url=url,
                error_type="InvalidURL",
                error_code="PW001",
                description="无效的URL",
                crawler_used="playwright"
            )
            
        page = None
        try:
            # 如果爬虫没有初始化，先初始化
            if self._crawler is None:
                await self.start()
                
            # 使用with语句自动管理页面生命周期
            async with self._browser_context.new_page() as page:
                # 添加资源使用优化: 设置超时和阻止不必要资源
                await page.route('**/*.{png,jpg,jpeg,gif,svg,pdf,mp4,webp}', lambda route: route.abort() 
                              if kwargs.get('block_images', False) else route.continue_())
                
                response = await page.goto(url, timeout=kwargs.get('timeout', 30000))
                
                # 检查页面加载状态
                if not response or response.status >= 400:
                    await page.close()
                    return CrawlResult.create_failure(
                        url=url,
                        error_type="HttpError",
                        error_code=f"PW{response.status if response else 000}",
                        description=f"HTTP错误: {response.status if response else '连接失败'}",
                        crawler_used="playwright"
                    )
                
                # 保存原始HTML内容
                html_content = await page.content()
                
                # 提取链接
                links = []
                if kwargs.get('extract_links', True):
                    hrefs = await page.eval_on_selector_all('a[href]', '''
                        (elements) => elements.map(el => ({
                            href: el.href,
                            text: el.textContent.trim()
                        }))
                    ''')
                    links = hrefs
                    
                
                # 创建结果对象 - 优化内存使用
                result = CrawlResult.create_success(
                    url=url,
                    content="",
                    # 可选择不保存完整HTML减少内存使用
                    html=html_content if kwargs.get('save_html', True) else "",
                    format="markdown",
                    crawler_used="playwright"
                )
                
                # 单独设置链接
                result.links = links
                
                # 提取元数据
                title = await page.title()
                result.metadata = {
                    'title': title,
                    'javascript_enabled': True
                }
                
                if result.is_success() and self.content_cleaner:
                    # 处理内容
                    temp_html = html_content  # 使用临时变量
                    cleaner_result = self.content_cleaner.process_page_content(
                        html_content=temp_html
                    )
                    result.content = cleaner_result.get("cleaned_content")
                    
                    # 只在必要时保存
                    if kwargs.get('save_html', True):
                        result.raw_html = html_content  # 存储原始HTML
                        result.html = cleaner_result.get("cleaned_html")
                    
                    # 清理不再需要的大对象
                    del temp_html
                
                await page.close()  # 确保关闭页面
                page = None  # 重置指针
                
                return result
            
            return result
            
        except Exception as e:
            logger.error(f"Playwright爬取异常: {str(e)}")
            if page:  # 确保异常情况下也关闭页面
                try:
                    await page.close()
                except:
                    pass
            return CrawlResult.create_failure(
                url=url,
                error_type="CrawlError",
                error_code="PW002",
                description=str(e),
                crawler_used="playwright"
            )
            
    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        max_concurrent = kwargs.get('max_concurrent', 
                                 self.config.get('max_concurrent_tasks', 5))
        base_url = kwargs.get('base_url')
            
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str) -> Tuple[str, CrawlResult]:
            async with semaphore:
                kwargs['is_homepage'] = (url == base_url) if base_url else False
                result = await self.crawl(url, **kwargs)
                return url, result
                
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = []
        
        for task in asyncio.as_completed(tasks):
            try:
                result = await task
                results.append(result)
            except Exception as e:
                logger.error(f"爬取任务异常: {str(e)}")
                
        return {url: result for url, result in results}
        
    async def crawl_site(self, base_url: str, **kwargs) -> Dict[str, CrawlResult]:
        """爬取整个网站 - 支持多层级递归爬取"""
        max_urls = kwargs.get('max_urls', 50)
        include_external = kwargs.get('include_external', False)
        max_concurrent = kwargs.get('max_concurrent', 
                                 self.config.get('max_concurrent_tasks', 5))
        max_depth = kwargs.get('max_depth', 1)  # 默认爬取深度为1
        
        # 按域名控制并发 - 添加资源使用限制
        domain_semaphores = {}
        domain_max_concurrent = kwargs.get('domain_max_concurrent', 3)
        
        # 初始化结果集和待爬取队列
        results = {}
        crawled_urls = set()
        to_crawl_queue = [(base_url, 0)]  # (url, depth)
        
        try:
            # 爬取首页
            result = await self.crawl(base_url, is_homepage=True, 
                                  include_external=include_external,
                                  extract_links=True,
                                  save_html=kwargs.get('save_html', True))
            results[base_url] = result
            crawled_urls.add(base_url)
            
            # 提取首页链接
            if result.is_success() and result.links:
                links = [link.get('href') for link in result.links if link.get('href')]
                base_domain = get_domain(base_url)
                
                # 过滤链接
                if not include_external:
                    links = [link for link in links if get_domain(link) == base_domain]
                
                # on_collect_sub_url钩子函数
                links = await self.process_collected_urls(base_url, [link for link in links if link not in crawled_urls])
                
                # 添加第一层链接到待爬取队列
                for link in links:
                    if link not in crawled_urls and len(to_crawl_queue) < max_urls:
                        to_crawl_queue.append((link, 1))
            
            # 广度优先爬取
            while to_crawl_queue and len(crawled_urls) < max_urls:
                # 获取当前批次要爬取的URL，按域名分组
                current_batch = []
                current_depths = {}
                domain_counts = {}
                
                # 智能选择当前批次URL，避免单一域名过多请求
                queue_copy = list(to_crawl_queue)
                selected_indices = []
                
                for i, (url, depth) in enumerate(queue_copy):
                    if url in crawled_urls:
                        selected_indices.append(i)
                        continue
                        
                    domain = get_domain(url)
                    current_domain_count = domain_counts.get(domain, 0)
                    
                    if current_domain_count < domain_max_concurrent and len(current_batch) < max_concurrent:
                        current_batch.append(url)
                        current_depths[url] = depth
                        domain_counts[domain] = current_domain_count + 1
                        crawled_urls.add(url)
                        selected_indices.append(i)
                
                # 从队列中移除选定的URL
                to_crawl_queue = [item for i, item in enumerate(queue_copy) if i not in selected_indices]
                
                if not current_batch:
                    break
                    
                # 并发爬取当前批次
                batch_results = await self.crawl_many(
                    current_batch, 
                    max_concurrent=max_concurrent,
                    base_url=base_url,
                    include_external=include_external,
                    extract_links=True,
                    save_html=kwargs.get('save_html', False)  # 减少内存使用
                )
                
                # 更新结果集
                results.update(batch_results)
                
                # 周期性垃圾回收
                import gc
                gc.collect()
                
                # 处理当前批次中的链接，准备下一层爬取
                for url, result in batch_results.items():
                    current_depth = current_depths.get(url, 0)
                    
                    # 如果达到最大深度，不再提取链接
                    if current_depth >= max_depth:
                        continue
                        
                    # 提取新的链接
                    if result.is_success() and result.links:
                        new_links = [link.get('href') for link in result.links if link.get('href')]
                        
                        # 过滤链接
                        if not include_external:
                            new_links = [link for link in new_links if get_domain(link) == base_domain]
                        
                        # on_collect_sub_url钩子函数
                        new_links = await self.process_collected_urls(url, new_links)
                        
                        # 添加新链接到待爬取队列
                        for link in new_links:
                            if link not in crawled_urls and len(crawled_urls) + len(to_crawl_queue) < max_urls:
                                to_crawl_queue.append((link, current_depth + 1))
            
            # 统计爬取信息
            stats = {
                "total_urls": len(results),
                "success_count": sum(1 for r in results.values() if r.is_success()),
                "failed_count": sum(1 for r in results.values() if not r.is_success()),
                "max_depth_reached": max([current_depths.get(url, 0) for url in results.keys()]) if current_depths else 0
            }
            return results
        finally:
            # 确保资源释放
            # 如果是临时创建的crawler，需要关闭
            if kwargs.get('close_after_crawl', True):
                await self.close()
