"""
Information Leader Agent 主程序
"""
import asyncio
import logging
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from common.models import TaskParams, TaskResult, ServerConfig
from common.a2a_protocol import <PERSON><PERSON><PERSON>ocol<PERSON><PERSON><PERSON>
from .coordinator import TaskCoordinator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_leader_agent_card():
    """创建Leader Agent Card"""
    from common.models import AgentCard, AgentCapability, AgentSupports
    
    capabilities = [
        AgentCapability(
            name="coordinate_information_collection",
            description="Coordinate website crawling and information enrichment tasks",
            inputs=[
                {"type": "text", "name": "target_url"},
                {"type": "text", "name": "company_name"},
                {"type": "array", "name": "priority_keywords"}
            ],
            outputs=[
                {"type": "object", "name": "enriched_information"}
            ]
        ),
        AgentCapability(
            name="manage_crawling_tasks",
            description="Manage and coordinate web crawling tasks across employees",
            inputs=[
                {"type": "text", "name": "base_url"},
                {"type": "integer", "name": "max_pages", "optional": True},
                {"type": "integer", "name": "max_depth", "optional": True}
            ],
            outputs=[
                {"type": "object", "name": "crawling_results"}
            ]
        )
    ]
    
    supports = AgentSupports(
        streaming=True,
        pushNotifications=True,
        crossGroupCalls=True
    )
    
    return AgentCard(
        name="InformationLeaderAgent",
        version="1.0.0",
        description="Information collection group leader managing crawling and enrichment employees",
        endpoint="http://info-leader:8231/a2a",
        capabilities=capabilities,
        skills=["task-coordination", "employee-management", "information-integration"],
        supports=supports,
        auth=["none"],
        protocolVersion="1.0",
        group="information_collection",
        employees=["Crawl4AIEmployee", "ExaSearchEmployee"]
    )


class InformationLeaderAgent:
    """Information Leader Agent"""
    
    def __init__(self, server_config: ServerConfig):
        self.server_config = server_config
        
        # 创建Agent Card
        self.agent_card = create_leader_agent_card()
        self.agent_card.endpoint = f"http://localhost:{server_config.port}{server_config.a2a_endpoint}"
        
        # 创建A2A协议处理器
        self.protocol_handler = A2AProtocolHandler(self.agent_card)
        
        # 创建任务协调器
        self.coordinator = TaskCoordinator()
        
        # 注册任务处理器
        self._register_handlers()
        
        # 创建FastAPI应用
        self.app = self._create_app()
    
    def _register_handlers(self):
        """注册任务处理器"""
        self.protocol_handler.register_handler(
            "coordinate_information_collection", 
            self._handle_coordinate_information_collection
        )
        self.protocol_handler.register_handler(
            "manage_crawling_tasks",
            self._handle_manage_crawling_tasks
        )
        self.protocol_handler.register_handler(
            "default",
            self._handle_default
        )
    
    async def _handle_coordinate_information_collection(self, task_params: TaskParams) -> TaskResult:
        """处理信息收集协调任务"""
        return await self.coordinator.coordinate_information_collection(task_params)
    
    async def _handle_manage_crawling_tasks(self, task_params: TaskParams) -> TaskResult:
        """处理爬取任务管理"""
        # 这个方法主要用于管理多个爬取任务
        return await self.coordinator.coordinate_information_collection(task_params)
    
    async def _handle_default(self, task_params: TaskParams) -> TaskResult:
        """默认任务处理器"""
        # 默认使用信息收集协调
        return await self.coordinator.coordinate_information_collection(task_params)
    
    def _create_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title="Information Leader Agent",
            description="A2A-compatible information collection leader agent",
            version="1.0.0"
        )
        
        # 添加CORS中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=self.server_config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Agent Card端点
        @app.get(self.server_config.agent_card_path)
        async def get_agent_card():
            return self.protocol_handler.get_agent_card()
        
        # A2A协议端点
        @app.post(self.server_config.a2a_endpoint)
        async def handle_a2a_request(request: Request):
            try:
                request_data = await request.json()
                response = await self.protocol_handler.handle_request(request_data)
                return JSONResponse(content=response)
            except Exception as e:
                logger.error(f"Error handling A2A request: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 健康检查端点
        @app.get("/health")
        async def health_check():
            return {"status": "healthy", "agent": "Information Leader"}
        
        # Employee状态端点
        @app.get("/employees")
        async def get_employees():
            return {
                "employees": [
                    {
                        "name": emp.name,
                        "endpoint": emp.endpoint,
                        "capabilities": emp.capabilities,
                        "skills": emp.skills,
                        "available": emp.available
                    }
                    for emp in self.coordinator.employees.values()
                ]
            }
        
        # 任务状态端点
        @app.get("/tasks")
        async def get_active_tasks():
            return {
                "active_tasks": len(self.coordinator.active_tasks),
                "task_ids": list(self.coordinator.active_tasks.keys())
            }
        
        return app
    
    async def start(self):
        """启动Agent"""
        logger.info("Starting Information Leader Agent...")
        logger.info(f"Agent started on {self.server_config.host}:{self.server_config.port}")
    
    async def stop(self):
        """停止Agent"""
        logger.info("Stopping Information Leader Agent...")
        logger.info("Agent stopped")


async def main():
    """主函数"""
    # 配置
    server_config = ServerConfig(
        host="0.0.0.0",
        port=8231,
        log_level="INFO"
    )
    
    # 创建Agent
    agent = InformationLeaderAgent(server_config)
    
    # 启动Agent
    await agent.start()
    
    try:
        # 运行服务器
        config = uvicorn.Config(
            agent.app,
            host=server_config.host,
            port=server_config.port,
            log_level=server_config.log_level.lower()
        )
        server = uvicorn.Server(config)
        await server.serve()
    finally:
        await agent.stop()


if __name__ == "__main__":
    asyncio.run(main())
