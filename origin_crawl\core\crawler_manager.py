"""
爬虫管理器，负责爬虫的选择和降级
"""
from typing import List, Type, Dict, Any, Tuple, Optional,Callable
import logging
import time
import asyncio

from specific_multi_scraper.core.config import CrawlerConfig
from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler, CrawlResult
from specific_multi_scraper.core.crawler.playwright_crawler import PlaywrightCrawler
from specific_multi_scraper.utils.process_site_url import process_url_data
from specific_multi_scraper.core.config import CrawlerHooks
from specific_multi_scraper.utils.browser_pool import browser_pool

# 配置日志
logger = logging.getLogger(__name__)

class CrawlerManager:
    """爬虫管理器"""
    def __init__(self, crawler_classes: List[Type[BaseCrawler]], config: Dict[str, Any] = None, hooks: CrawlerHooks = None):
        self.crawler_classes = crawler_classes
        self.config = config or CrawlerConfig()
        self.crawlers = []
        self.current_crawler_index = 0
        self.hooks = hooks or CrawlerHooks()  # 使用hooks实例或创建新实例
        
    async def start(self):
        """初始化所有爬虫，忽略初始化失败的爬虫"""
        initialized_crawlers = []
        
        for crawler_class in self.crawler_classes:
            crawler_name = crawler_class.__name__
            try:
                logger.info(f"尝试初始化爬虫: {crawler_name}")
                crawler = await self._create_crawler(crawler_class)
                if crawler:
                    initialized_crawlers.append(crawler)
                    logger.info(f"爬虫 {crawler_name} 初始化成功")
            except Exception as e:
                logger.error(f"初始化爬虫 {crawler_name} 失败: {str(e)}")
                # 继续尝试下一个爬虫，不中断整个过程
        
        # 更新爬虫列表，只包含成功初始化的爬虫
        self.crawlers = initialized_crawlers
        
        if not self.crawlers:
            logger.warning("所有爬虫都初始化失败，系统将无法进行爬取")
        else:
            logger.info(f"成功初始化 {len(self.crawlers)} 个爬虫: {', '.join(type(c).__name__ for c in self.crawlers)}")
        
        # 启动浏览器池监控·
        await self._start_browser_pool()

    async def _start_browser_pool(self):
        """启动浏览器池监控"""
        # 浏览器池配置
        browser_pool.max_instances = self.config.get("max_browser_instances", 10)
        browser_pool.idle_timeout = self.config.get("browser_idle_timeout", 300)
        browser_pool.check_interval = self.config.get("browser_check_interval", 60)
        
        # 确保清理任务运行
        browser_pool._ensure_cleanup_task()
        logger.info(f"浏览器池已配置: 最大实例数={browser_pool.max_instances}, 空闲超时={browser_pool.idle_timeout}秒")

    async def close(self):
        """关闭所有爬虫"""
        close_errors = []
        
        # 单独关闭每个爬虫，记录错误但不中断
        for crawler in self.crawlers:
            try:
                await crawler.close()
            except Exception as e:
                error_msg = f"关闭爬虫 {type(crawler).__name__} 失败: {str(e)}"
                logger.error(error_msg)
                close_errors.append(error_msg)
        
        # 如果有错误，记录汇总信息
        if close_errors:
            logger.error(f"关闭爬虫过程中共有 {len(close_errors)} 个错误")
            
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """带有降级机制的爬取"""
        if not self.crawlers:
            return CrawlResult.create_failure(
                url=url,
                error_type="CrawlerError",
                error_code="CM001",
                description="未找到可用的爬虫",
                crawler_used="crawler_manager"
            )
            
        original_index = self.current_crawler_index
        
        while True:
            crawler = self.crawlers[self.current_crawler_index]
            try:
                result = await crawler.crawl(url, **kwargs)
                
                if result.is_success():
                    return result
            except Exception as e:
                logger.error(f"爬虫 {type(crawler).__name__} 爬取失败: {str(e)}")
                result = CrawlResult.create_failure(
                    url=url,
                    error_type="CrawlerError",
                    error_code="CM002",
                    description=str(e),
                    crawler_used=type(crawler).__name__
                )
                
            # 尝试下一个爬虫
            self.current_crawler_index = (self.current_crawler_index + 1) % len(self.crawlers)
            
            # 如果已经尝试了所有爬虫，返回最后一个失败结果
            if self.current_crawler_index == original_index:
                return result
                
    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """批量爬取多个URL，支持降级"""
        results = {}
        
        # 逐个URL进行爬取，每个URL都可能使用不同的爬虫（根据降级逻辑）
        for url in urls:
            result = await self.crawl(url, **kwargs)
            results[url] = result
            
        return results
        
    async def crawl_site(self, base_url: str, **kwargs) -> Dict[str, CrawlResult]:
        """爬取整个网站，完整支持降级
        
        此方法会按照爬虫优先级尝试使用每个爬虫的crawl_site方法，
        如果一个爬虫失败，会自动尝试下一个爬虫，直到成功或所有爬虫都失败
        """
        if not self.crawlers:
            return {
                base_url: CrawlResult.create_failure(
                    url=base_url,
                    error_type="CrawlerError",
                    error_code="CM001",
                    description="未找到可用的爬虫",
                    crawler_used="crawler_manager"
                )
            }
            
        # 记录每个爬虫的爬取结果，用于记录日志和调试
        crawler_results = {}  
        # 记录原始爬虫索引，用于循环检测
        original_index = self.current_crawler_index
        
        while True:
            crawler = self.crawlers[self.current_crawler_index]
            crawler_name = type(crawler).__name__
            logger.info(f"尝试使用 {crawler_name} 爬取网站: {base_url}")
            
            try:
                # 尝试使用当前爬虫的完整网站爬取功能
                start_time = time.time()
                result,failed_urls = await crawler.crawl_site(base_url, **kwargs)
                end_time = time.time()
                elapsed = end_time - start_time
                
                logger.info(f"{crawler_name} 成功爬取网站，获取到 {len(result) - 1} 个URL，耗时 {elapsed:.2f}秒,失败 {len(failed_urls)} 个URL")
                 # 如果有失败的URL，尝试使用其他爬虫爬取
                if failed_urls:
                    logger.info(f"发现 {len(failed_urls)} 个URL爬取失败，尝试使用其他爬虫爬取")
                    recovered_results = await self._recover_failed_urls(failed_urls, crawler, **kwargs)
                    
                    # 将恢复的结果合并到原始结果中
                    for url, recover_result in recovered_results.items():
                        if recover_result.is_success():
                            result[url] = recover_result
                    
                del failed_urls
                return result
                
            except Exception as e:
                # 当前爬虫失败，记录错误并尝试下一个爬虫
                logger.warning(f"爬虫 {crawler_name} 爬取网站失败: {str(e)}")
                crawler_results[crawler_name] = {
                    "crawler": crawler_name,
                    "success": False,
                    "error": str(e)
                }
                
                # 尝试下一个爬虫
                self.current_crawler_index = (self.current_crawler_index + 1) % len(self.crawlers)
                
                # 如果已经尝试了所有爬虫，退出循环
                if self.current_crawler_index == original_index:
                    break
        
        # 所有爬虫都失败了，使用最可靠的降级策略：只爬取首页
        logger.warning(f"所有爬虫都无法完整爬取网站，降级为只爬取首页")
        try:
            result = await self.crawl(base_url, is_homepage=True, **kwargs)
            base_result = {base_url: result}
            
            return base_result
            
        except Exception as final_e:
            # 即使降级爬取也失败，返回错误结果
            logger.error(f"降级爬取首页也失败: {str(final_e)}")
            return {
                base_url: CrawlResult.create_failure(
                    url=base_url,
                    error_type="FallbackError",
                    error_code="CM003",
                    description=f"所有爬虫都失败，降级爬取也失败: {str(final_e)}",
                    crawler_used="crawler_manager_fallback"
                ),
            }

    async def _recover_failed_urls(self, failed_urls: List[str], original_crawler: BaseCrawler, 
                                **kwargs) -> Dict[str, CrawlResult]:
        """尝试使用其他爬虫恢复失败的URL
        
        Args:
            failed_urls: 失败的URL列表
            original_crawler: 原始爬虫，用于避免重复使用相同的爬虫
            **kwargs: 传递给crawl方法的参数
            
        Returns:
            恢复的结果字典
        """
        if not failed_urls:
            return {}
        # 将pdf、图片、视频等非常规的网页url过滤掉,将后缀名都小写化，.PDF, .JPG, .PNG, .GIF, .MP4, .AVI, .MOV, .MP3, .WAV, .M4A, .OGG, .WEBM, .DOCX
        failed_urls = [url for url in failed_urls if not url.lower().endswith(('.pdf', '.jpg', '.png', '.gif', '.mp4', '.avi', '.mov', '.mp3', '.wav', '.m4a', '.ogg', '.webm','docx'))]
        logger.info(f"尝试恢复 {len(failed_urls)} 个失败的URL")
        self.hooks.on_recover_failed_urls(failed_urls)
        results = {}
        
        # 获取可用的爬虫列表（排除原始爬虫）
        available_crawlers = [c for c in self.crawlers]
        if not available_crawlers:
            logger.warning("没有可用的备选爬虫进行恢复")
            return {}
        
        # 将失败的URL分成5组
        url_groups = []
        group_size = max(1, len(failed_urls) // 5)  # 确保至少每组1个URL
        for i in range(0, len(failed_urls), group_size):
            url_groups.append(failed_urls[i:i + group_size])
        
        # 如果不足5组，保持现有分组
        logger.info(f"将 {len(failed_urls)} 个失败的URL分成 {len(url_groups)} 组进行并发恢复")
        
        # 按组并发爬取，组之间间隔2秒
        for group_index, url_group in enumerate(url_groups):
            if group_index > 0:
                # 组间间隔2秒
                await asyncio.sleep(2)
                
            logger.info(f"开始处理第 {group_index+1}/{len(url_groups)} 组失败URL，共 {len(url_group)} 个")
            
            # 创建该组内URL的并发任务
            async def recover_url(url: str) -> Tuple[str, Optional[CrawlResult]]:
                # 遍历所有备选爬虫尝试恢复
                for crawler in available_crawlers:
                    # 如果是.pdf文件，跳过playwright爬虫
                    if url.endswith('.pdf') and isinstance(crawler, PlaywrightCrawler):
                        continue
                        
                    crawler_name = type(crawler).__name__
                    try:
                        logger.debug(f"使用 {crawler_name} 尝试爬取失败的URL: {url}")
                        result = await crawler.crawl(url, **kwargs)
                        
                        if result.is_success():
                            logger.info(f"URL {url} 通过 {crawler_name} 成功恢复")
                            return url, result
                    except Exception as e:
                        logger.debug(f"爬虫 {crawler_name} 恢复 {url} 失败: {str(e)}")
                
                # 所有爬虫都失败
                return url, None
            
            # 创建并发任务
            tasks = [recover_url(url) for url in url_group]
            group_results = await asyncio.gather(*tasks)
            
            # 处理结果
            for url, result in group_results:
                if result:  # 如果成功恢复
                    results[url] = result
            
            logger.info(f"第 {group_index+1} 组处理完成，成功恢复 {sum(1 for _, r in group_results if r)} 个URL")
        
        # 汇总结果
        success_count = len(results)
        fail_count = len(failed_urls) - success_count
        logger.info(f"所有组处理完成，共成功恢复 {success_count} 个URL，失败 {fail_count} 个")
        
        return results

    async def crawl_multiple_sites(self, base_urls: List[str], **kwargs) -> Dict[str, Dict[str, CrawlResult]]:
        """并发爬取多个网站，每个网站使用最适合的爬虫
        
        Args:
            base_urls: 要爬取的网站基础URL列表
            **kwargs: 传递给crawl_site的参数
            
        Returns:
            以网站URL为键，爬取结果为值的字典
        """
        if not base_urls:
            return {}
            
        logger.info(f"开始并发爬取 {len(base_urls)} 个网站")
        
        # 创建爬取任务
        async def crawl_site_with_progress(site_url: str) -> Tuple[str, Dict[str, CrawlResult]]:
            try:
                site_start = time.time()
                logger.info(f"开始爬取网站: {site_url}")
                result = await self.crawl_site(site_url, **kwargs)
                site_elapsed = time.time() - site_start
                site_pages = len(result)
                logger.info(f"网站 {site_url} 爬取完成，获取 {site_pages} 个页面，耗时 {site_elapsed:.2f}秒")
                return site_url, result
            except Exception as e:
                logger.error(f"爬取网站 {site_url} 失败: {str(e)}")
                # 返回基本的错误结果
                error_result = {
                    site_url: CrawlResult.create_failure(
                        url=site_url,
                        error_type="SiteCrawlError",
                        error_code="CMS001",
                        description=f"爬取网站异常: {str(e)}",
                        crawler_used="crawler_manager"
                    )
                }
                return site_url, error_result
                
        # 创建并发任务
        tasks = [crawl_site_with_progress(url) for url in base_urls]
        
        # 同时执行所有任务
        results = {}
        for task in asyncio.as_completed(tasks):
            try:
                site_url, site_results = await task
                results[site_url] = site_results
            except Exception as e:
                logger.error(f"并发爬取过程中发生异常: {str(e)}")
        
        # 添加全局聚合统计信息
        total_pages = sum(len(site_result) for site_result in results.values())
        logger.info(f"所有网站爬取完成，共爬取 {len(results)} 个网站，{total_pages} 个页面")
            
        return results

    async def _create_crawler(self, crawler_class):
        """创建指定爬虫实例，添加更多错误处理"""
        crawler_name = crawler_class.__name__
        
        try:
            # 创建爬虫实例
            crawler = crawler_class(config=self.config, hooks=self.hooks)
            
            # 尝试启动爬虫
            try:
                await asyncio.wait_for(crawler.start(), timeout=10.0)  # 添加超时控制
                logger.info(f"爬虫 {crawler_name} 启动成功")
                return crawler
            except asyncio.TimeoutError:
                logger.error(f"爬虫 {crawler_name} 启动超时")
                # 尝试关闭爬虫以释放资源
                try:
                    if hasattr(crawler, 'close') and callable(crawler.close):
                        await crawler.close()
                except Exception as close_error:
                    logger.error(f"关闭超时爬虫时出错: {str(close_error)}")
                return None
            except Exception as start_error:
                logger.error(f"爬虫 {crawler_name} 启动失败: {str(start_error)}")
                # 尝试关闭爬虫以释放资源
                try:
                    if hasattr(crawler, 'close') and callable(crawler.close):
                        await crawler.close()
                except Exception as close_error:
                    logger.error(f"关闭失败爬虫时出错: {str(close_error)}")
                return None
            
        except Exception as e:
            logger.error(f"创建爬虫实例 {crawler_name} 时出错: {str(e)}")
            return None
