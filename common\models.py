"""
A2A协议数据模型
"""
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
import uuid
from datetime import datetime


class TaskState(str, Enum):
    """任务状态枚举"""
    SUBMITTED = "submitted"
    WORKING = "working"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PartType(str, Enum):
    """消息部分类型"""
    TEXT = "text"
    OBJECT = "object"
    FILE = "file"
    IMAGE = "image"


class MessageRole(str, Enum):
    """消息角色"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class Part(BaseModel):
    """消息部分"""
    type: PartType
    text: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    file_path: Optional[str] = None
    mime_type: Optional[str] = None


class Message(BaseModel):
    """A2A消息"""
    role: MessageRole
    parts: List[Part]
    timestamp: Optional[datetime] = Field(default_factory=datetime.now)


class TaskStatus(BaseModel):
    """任务状态"""
    state: TaskState
    progress: Optional[float] = None
    message: Optional[str] = None
    timestamp: Optional[datetime] = Field(default_factory=datetime.now)


class Artifact(BaseModel):
    """任务产出物"""
    name: str
    description: Optional[str] = None
    parts: List[Part]
    metadata: Optional[Dict[str, Any]] = None


class A2ARequest(BaseModel):
    """A2A请求"""
    jsonrpc: str = "2.0"
    id: Union[str, int]
    method: str
    params: Dict[str, Any]


class TaskParams(BaseModel):
    """任务参数"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    message: Message
    metadata: Optional[Dict[str, Any]] = None


class TaskResult(BaseModel):
    """任务结果"""
    id: str
    status: TaskStatus
    artifacts: List[Artifact] = Field(default_factory=list)
    metadata: Optional[Dict[str, Any]] = None


class A2AResponse(BaseModel):
    """A2A响应"""
    jsonrpc: str = "2.0"
    id: Union[str, int]
    result: Optional[TaskResult] = None
    error: Optional[Dict[str, Any]] = None


class A2AError(BaseModel):
    """A2A错误"""
    code: int
    message: str
    data: Optional[Dict[str, Any]] = None


class AgentCapability(BaseModel):
    """Agent能力描述"""
    name: str
    description: str
    inputs: List[Dict[str, Any]]
    outputs: List[Dict[str, Any]]


class AgentSupports(BaseModel):
    """Agent支持的功能"""
    streaming: bool = False
    pushNotifications: bool = False
    fallbackMechanisms: Optional[List[str]] = None
    crossGroupCalls: Optional[bool] = None


class AgentCard(BaseModel):
    """Agent卡片"""
    name: str
    version: str
    description: str
    endpoint: str
    capabilities: List[AgentCapability]
    skills: List[str]
    supports: AgentSupports
    auth: List[str]
    protocolVersion: str = "1.0"
    group: Optional[str] = None
    dependencies: Optional[List[str]] = None
    employees: Optional[List[str]] = None


# Crawl4AI特定的数据模型

class CrawlOptions(BaseModel):
    """爬取选项"""
    url: str
    include_links: bool = True
    css_selectors: Optional[List[str]] = None
    timeout: int = 30
    extract_metadata: bool = True
    clean_content: bool = True


class SiteOptions(BaseModel):
    """网站爬取选项"""
    base_url: str
    max_pages: int = 50
    max_depth: int = 2
    include_external: bool = False
    max_concurrent: int = 5
    priority_keywords: Optional[List[str]] = None


class CrawlResult(BaseModel):
    """爬取结果"""
    url: str
    success: bool
    content: Optional[str] = None
    html: Optional[str] = None
    links: Optional[List[Dict[str, str]]] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    crawler_used: str = "crawl4ai"
    timestamp: datetime = Field(default_factory=datetime.now)


class SiteResult(BaseModel):
    """网站爬取结果"""
    base_url: str
    total_pages: int
    successful_pages: int
    failed_pages: int
    results: Dict[str, CrawlResult]
    summary: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class StructuredData(BaseModel):
    """结构化数据"""
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    links: Optional[List[Dict[str, str]]] = None
    images: Optional[List[Dict[str, str]]] = None
    metadata: Optional[Dict[str, Any]] = None
    extracted_data: Optional[Dict[str, Any]] = None


class CrawlError(Exception):
    """爬取错误"""
    def __init__(self, url: str, error_type: str, message: str, details: Optional[Dict[str, Any]] = None):
        self.url = url
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        super().__init__(f"{error_type} for {url}: {message}")


# 配置模型

class Crawl4AIConfig(BaseModel):
    """Crawl4AI配置"""
    timeout: int = 30
    max_concurrent: int = 5
    enable_fallback: bool = True
    fallback_timeout: int = 15
    content_cleaning: bool = True
    extract_links: bool = True
    extract_metadata: bool = True
    user_agent: Optional[str] = None
    proxy: Optional[str] = None
    headers: Optional[Dict[str, str]] = None


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8232
    log_level: str = "INFO"
    cors_origins: List[str] = ["*"]
    agent_card_path: str = "/.well-known/agent.json"
    a2a_endpoint: str = "/a2a"
