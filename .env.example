# Information Collection Agent Environment Configuration

# =============================================================================
# Agent Configuration
# =============================================================================

# Leader Agent Configuration
LEADER_HOST=0.0.0.0
LEADER_PORT=8231
LEADER_LOG_LEVEL=INFO

# Crawl4AI Employee Configuration
CRAWL4AI_HOST=0.0.0.0
CRAWL4AI_PORT=8232
CRAWL4AI_LOG_LEVEL=INFO

# Exa Employee Configuration (if enabled)
EXA_HOST=0.0.0.0
EXA_PORT=8233
EXA_LOG_LEVEL=INFO

# =============================================================================
# Crawling Configuration
# =============================================================================

# Crawl4AI Settings
CRAWL4AI_TIMEOUT=30
CRAWL4AI_MAX_CONCURRENT=5
CRAWL4AI_ENABLE_FALLBACK=true
CRAWL4AI_FALLBACK_TIMEOUT=15
CRAWL4AI_CONTENT_CLEANING=true
CRAWL4AI_EXTRACT_LINKS=true
CRAWL4AI_EXTRACT_METADATA=true

# User Agent
CRAWL4AI_USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# Proxy Settings (optional)
# CRAWL4AI_PROXY=http://proxy.example.com:8080
# CRAWL4AI_PROXY_USERNAME=username
# CRAWL4AI_PROXY_PASSWORD=password

# =============================================================================
# External API Keys
# =============================================================================

# Exa API Key (required for Exa Employee)
EXA_API_KEY=your_exa_api_key_here

# =============================================================================
# Security & CORS
# =============================================================================

# CORS Origins (comma-separated)
CORS_ORIGINS=*

# API Keys for authentication (if needed)
# API_KEY=your_api_key_here
# JWT_SECRET=your_jwt_secret_here

# =============================================================================
# Database & Storage (if needed)
# =============================================================================

# Redis Configuration (for caching and task queue)
# REDIS_URL=redis://localhost:6379/0
# REDIS_PASSWORD=your_redis_password

# File Storage
# STORAGE_PATH=./storage
# MAX_FILE_SIZE=10485760  # 10MB

# =============================================================================
# Monitoring & Logging
# =============================================================================

# Log Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log Format
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Enable metrics collection
# ENABLE_METRICS=true
# METRICS_PORT=9090

# =============================================================================
# Development Settings
# =============================================================================

# Development mode
DEBUG=false

# Auto-reload on code changes
AUTO_RELOAD=false

# Enable API documentation
ENABLE_DOCS=true

# =============================================================================
# Docker & Deployment
# =============================================================================

# Container names
LEADER_CONTAINER_NAME=info-leader
CRAWL4AI_CONTAINER_NAME=crawl4ai-employee
EXA_CONTAINER_NAME=exa-employee

# Network name
NETWORK_NAME=information-collection-network

# Health check settings
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# =============================================================================
# Performance Tuning
# =============================================================================

# Worker processes
WORKER_PROCESSES=1

# Connection limits
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5

# Request limits
MAX_REQUEST_SIZE=10485760  # 10MB
REQUEST_TIMEOUT=60

# Memory limits (for Docker)
MEMORY_LIMIT=512m
MEMORY_RESERVATION=256m
