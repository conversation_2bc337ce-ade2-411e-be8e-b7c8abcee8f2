import asyncio
import logging
import time
import psutil
import os
import signal

logger = logging.getLogger(__name__)

class MemoryMonitor:
    """内存监控与Chrome进程清理工具"""
    
    def __init__(self, max_memory_percent=85, check_interval=60):
        self.max_memory_percent = max_memory_percent
        self.check_interval = check_interval
        self.running = False
        self.monitor_task = None
    
    async def start_monitoring(self):
        """启动内存监控"""
        if self.running:
            return
            
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("内存监控已启动")
    
    async def stop_monitoring(self):
        """停止内存监控"""
        if not self.running:
            return
            
        self.running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("内存监控已停止")
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                memory_usage = psutil.virtual_memory().percent
                if memory_usage > self.max_memory_percent:
                    logger.warning(f"内存使用率过高: {memory_usage}%，清理Chrome进程")
                    await self._clean_chrome_processes()
                
                # 定期检查Chrome进程数量
                chrome_count = len(self._find_chrome_processes())
                if chrome_count > 30:  # 如果Chrome进程超过30个
                    logger.warning(f"Chrome进程数量过多: {chrome_count}，进行清理")
                    await self._clean_chrome_processes(keep=10)  # 只保留10个最新的进程
                elif chrome_count > 0:
                    logger.debug(f"当前Chrome进程数量: {chrome_count}")
            except Exception as e:
                logger.error(f"内存监控发生错误: {str(e)}")
            
            # 等待下一次检查
            await asyncio.sleep(self.check_interval)
    
    async def _clean_chrome_processes(self, keep=None):
        """清理Chrome进程"""
        try:
            chrome_processes = self._find_chrome_processes()
            logger.info(f"找到 {len(chrome_processes)} 个Chrome进程")
            
            # 按创建时间排序，保留最新的进程
            chrome_processes.sort(key=lambda p: p.create_time(), reverse=True)
            
            # 确定要保留的进程数量
            keep_count = keep if keep is not None else max(1, len(chrome_processes) // 3)
            
            # 关闭多余进程
            for proc in chrome_processes[keep_count:]:
                try:
                    proc_info = f"PID: {proc.pid}, 内存: {proc.memory_info().rss / (1024*1024):.1f}MB, 运行时间: {time.time() - proc.create_time():.1f}秒"
                    logger.info(f"终止Chrome进程: {proc_info}")
                    proc.terminate()
                except Exception as e:
                    logger.error(f"终止进程 {proc.pid} 失败: {str(e)}")
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
        except Exception as e:
            logger.error(f"清理Chrome进程时出错: {str(e)}")
    
    def _find_chrome_processes(self):
        """查找所有Chrome相关进程"""
        chrome_processes = []
        try:
            current_process = psutil.Process(os.getpid())
            for proc in current_process.children(recursive=True):
                try:
                    if (proc.is_running() and 
                        ("chrome" in proc.name().lower() or "chromium" in proc.name().lower())):
                        chrome_processes.append(proc)
                except:
                    pass
        except Exception as e:
            logger.error(f"查找Chrome进程时出错: {str(e)}")
        return chrome_processes

# 创建全局内存监控器实例
memory_monitor = MemoryMonitor() 