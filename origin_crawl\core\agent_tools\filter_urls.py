from agent.tools.searchers.filter_result_tool import FilterResultTool
from typing import List
SYS_PROMPT = """
你是一个智能URL过滤专家，任务是识别网站中需要抓取的有效子链接。通过综合评估URL路径特征和页面内容相关性，严格过滤无效链接。

核心过滤规则：
1. **URL路径分析**：
   - 排除一些无用信息特征的路径，例如：
     * /privacy, /terms, /legal (隐私声明类)
     * /en, /fr, /ir 等多语言/区域版本
     * /login, /api 等交互功能页面
   - 保留包含有用信息的关键词的路径，例如：
     * /about, /company (公司信息)
     * /products, /services (产品服务)
     * /partners, /cases (合作信息)

2. **内容相关性评估**：
   - 评分维度：
     * 公司基础信息的完整性（成立时间、团队、资质）
     * 产品/服务的详细参数说明
     * 合作伙伴关系描述
     * 商业价值密度（技术文档>宣传文案）
   - 评分阈值：>0.6保留，≤0.6丢弃

3. **混合判断逻辑**：
   ┌───────────────┬───────────────┐
   │  URL路径特征  │ 内容相关性    │ 最终决策       │
   ├───────────────┼───────────────┤
   │ 符合保留特征  │ 任意分数      │ 保留（优先抓取）│
   │ 符合排除特征  │ 任意分数      │ 强制过滤       │
   │ 中性路径      │ ≥0.6         │ 保留           │
   └───────────────┴───────────────┘

输出要求：
- 返回需要抓取的URL索引，按优先级排序
- 格式示例：[3,0,2] 表示索引3的URL最优先抓取
{format_instructions}
"""

USER_PROMPT = """
#### 网站抓取任务 ####

主站：{main_url}

需求描述：抓取所有商业价值信息，包括但不完全限于：
- 公司基本信息（成立时间、团队、联系方式）
- 产品技术参数、服务方案
- 合作伙伴、客户案例
- 资质证书、荣誉奖项
- 新闻、公告、动态

待过滤URL列表：
{item_array}

附加要求：
1. 即使内容相关，但URL包含/en/、/privacy等特征时仍要过滤
2. 优先保留含PDF/Word文档的链接
3. 对于新闻、公告、动态等页面，需要重点保留，如果/About-Us/news/*,/news/*,/news-20220719/*等页面，需要重点保留

"""

class FilterUrlsTool:
    def __init__(self, **kwargs):
        self.tool = FilterResultTool(
        async_mode=True,
        SYS_PROMPT=SYS_PROMPT,
        USER_PROMPT=USER_PROMPT,
    )

    def filter_urls(self, urls: List[str]) -> List[str]:
        return self.tool.filter_urls(urls)

