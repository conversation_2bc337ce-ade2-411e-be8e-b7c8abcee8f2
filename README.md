# 🧠 Information Collection Agent Group

> 基于 A2A 协议的信息收集智能体组，专注于官网信息抓取、内容解析和信息补全。符合 Google A2A 协议规范，支持跨组协作和动态服务发现。

[![A2A Protocol](https://img.shields.io/badge/A2A-Protocol%20Compatible-blue)](https://developers.google.com/agent-protocol)
[![Python](https://img.shields.io/badge/Python-3.11+-green)](https://python.org)
[![Crawl4AI](https://img.shields.io/badge/Crawl4AI-Latest-orange)](https://docs.crawl4ai.com/)
[![FastMCP](https://img.shields.io/badge/FastMCP-A2A%20Framework-purple)](https://github.com/flexflowai/fastmcp)

---

## 🎯 项目概述

Information Collection Agent Group 是一个符合 A2A（Agent-to-Agent）协议的多智能体信息收集系统。该系统采用 **Manager-Leader-Employee** 三层架构，通过 **HTTP + JSON-RPC 2.0 + SSE** 实现标准化通信，支持动态服务发现和跨组协作。

### 核心特性

- ✅ **A2A 协议兼容**：完全符合 Google A2A 协议规范
- ✅ **智能降级机制**：Crawl4AI 主力 + 多种备选方案
- ✅ **跨组协作**：Employee 可被其他组动态调用
- ✅ **动态发现**：基于 Agent Card 的服务注册与发现
- ✅ **流式通信**：支持 SSE 实时进度反馈
- ✅ **容器化部署**：Docker + Docker Compose 一键部署
- ✅ **结构化输出**：标准化的 Artifact 数据格式

### 应用场景

- 🏢 **企业信息收集**：自动抓取和分析目标公司官网信息
- 📊 **市场调研**：批量收集行业相关网站数据
- 🔍 **竞品分析**：获取竞争对手公开信息
- 📈 **数据补全**：结合 Exa API 进行信息扩充和验证

---

## 🏗️ 系统架构

### A2A 三层架构

```mermaid
graph TB
    subgraph "Manager Layer"
        M[Manager Agent<br/>任务分发与协调]
    end
    
    subgraph "Leader Layer"
        L[Information Leader<br/>@8231<br/>组内调度管理]
    end
    
    subgraph "Employee Layer"
        E1[Crawl4AI Employee<br/>@8232<br/>网页抓取专家]
        E2[Exa Employee<br/>@8233<br/>信息补全专家]
    end
    
    M -->|JSON-RPC 2.0| L
    L -->|A2A Protocol| E1
    L -->|A2A Protocol| E2
    
    style M fill:#e1f5fe
    style L fill:#f3e5f5
    style E1 fill:#e8f5e8
    style E2 fill:#fff3e0
```

### 通信协议栈

| 层级 | 协议 | 用途 |
|------|------|------|
| 应用层 | A2A Protocol | Agent 间标准化通信 |
| 消息层 | JSON-RPC 2.0 | 请求/响应格式 |
| 流式层 | Server-Sent Events | 实时进度推送 |
| 传输层 | HTTP/HTTPS | 网络传输 |
| 数据层 | Agent Card JSON | 能力描述与发现 |

---

## 📋 技术栈

| 组件类别 | 技术选型 | 版本要求 | 说明 |
|---------|---------|---------|------|
| **Agent 框架** | [FastMCP](https://github.com/flexflowai/fastmcp) | Latest | A2A 协议实现框架 |
| **核心爬虫** | [Crawl4AI](https://docs.crawl4ai.com/) | 0.4.0+ | 智能网页抓取引擎 |
| **信息补全** | [Exa SDK](https://github.com/exa-labs/exa-py) | Latest | AI 搜索与信息扩充 |
| **运行时** | Python | 3.11+ | 异步编程支持 |
| **包管理** | uv | Latest | 快速依赖管理 |
| **容器化** | Docker | 20.0+ | 服务容器化 |
| **编排** | Docker Compose | 2.0+ | 多容器编排 |

---

## 🪪 Agent Card 规范

### Information Leader Agent Card

```json
{
  "name": "InformationLeaderAgent",
  "version": "1.0.0",
  "description": "Information collection group leader managing crawling and enrichment employees",
  "endpoint": "http://info-leader:8231/a2a",
  "capabilities": [
    {
      "name": "coordinate_information_collection",
      "description": "Coordinate website crawling and information enrichment tasks",
      "inputs": [
        { "type": "text", "name": "target_url" },
        { "type": "text", "name": "company_name" },
        { "type": "array", "name": "priority_keywords" }
      ],
      "outputs": [
        { "type": "object", "name": "enriched_information" }
      ]
    }
  ],
  "skills": ["task-coordination", "employee-management", "information-integration"],
  "supports": { 
    "streaming": true, 
    "pushNotifications": true,
    "crossGroupCalls": true
  },
  "auth": ["none"],
  "protocolVersion": "1.0",
  "group": "information_collection",
  "employees": ["Crawl4AIEmployee", "ExaSearchEmployee"]
}
```

### Crawl4AI Employee Agent Card

```json
{
  "name": "Crawl4AIEmployee",
  "version": "1.0.0",
  "description": "Advanced web crawler using Crawl4AI for structured content extraction",
  "endpoint": "http://crawl4ai-employee:8232/a2a",
  "capabilities": [
    {
      "name": "crawl_website",
      "description": "Extract structured content from websites with intelligent parsing",
      "inputs": [
        { "type": "text", "name": "url" },
        { "type": "boolean", "name": "include_links", "optional": true },
        { "type": "array", "name": "css_selectors", "optional": true }
      ],
      "outputs": [
        { "type": "object", "name": "structured_content" },
        { "type": "array", "name": "extracted_links" },
        { "type": "object", "name": "metadata" }
      ]
    }
  ],
  "skills": ["web-crawling", "content-extraction", "html-parsing", "javascript-rendering"],
  "supports": { 
    "streaming": true, 
    "pushNotifications": false,
    "fallbackMechanisms": ["playwright", "requests"]
  },
  "auth": ["none"],
  "protocolVersion": "1.0",
  "group": "information_collection"
}
```

### Exa Search Employee Agent Card

```json
{
  "name": "ExaSearchEmployee",
  "version": "1.0.0",
  "description": "AI-powered search and information enrichment using Exa API",
  "endpoint": "http://exa-employee:8233/a2a",
  "capabilities": [
    {
      "name": "search_and_enrich",
      "description": "Search for additional information and enrich existing content",
      "inputs": [
        { "type": "text", "name": "query" },
        { "type": "object", "name": "context", "optional": true },
        { "type": "integer", "name": "max_results", "optional": true }
      ],
      "outputs": [
        { "type": "array", "name": "search_results" },
        { "type": "object", "name": "enriched_content" }
      ]
    }
  ],
  "skills": ["ai-search", "information-enrichment", "company-research", "data-synthesis"],
  "supports": { 
    "streaming": false, 
    "pushNotifications": false 
  },
  "auth": ["apiKey"],
  "protocolVersion": "1.0",
  "group": "information_collection",
  "dependencies": ["EXA_API_KEY"]
}
```

---

## 🗂️ 项目结构

```
information_collection_agent/
├── 📁 agents/                          # Agent 实现
│   ├── 📁 leader/                      # Leader Agent
│   │   ├── main.py                     # Leader 主程序
│   │   ├── coordinator.py              # 任务协调逻辑
│   │   ├── agent_card.json             # Leader Agent Card
│   │   └── Dockerfile                  # Leader 容器配置
│   ├── 📁 employees/                   # Employee Agents
│   │   ├── 📁 crawl4ai/               # Crawl4AI Employee
│   │   │   ├── main.py                # Employee 主程序
│   │   │   ├── crawler.py             # 爬虫核心逻辑
│   │   │   ├── agent_card.json        # Agent Card
│   │   │   └── Dockerfile             # 容器配置
│   │   └── 📁 exa/                    # Exa Employee
│   │       ├── main.py                # Employee 主程序
│   │       ├── enricher.py            # 信息补全逻辑
│   │       ├── agent_card.json        # Agent Card
│   │       └── Dockerfile             # 容器配置
├── 📁 common/                          # 公共组件
│   ├── a2a_protocol.py                 # A2A 协议实现
│   ├── models.py                       # 数据模型
│   ├── utils.py                        # 工具函数
│   └── config.py                       # 配置管理
├── 📁 tests/                           # 测试用例
│   ├── test_leader.py                  # Leader 测试
│   ├── test_crawl4ai.py               # Crawl4AI 测试
│   └── test_exa.py                    # Exa 测试
├── 📁 docs/                            # 文档
│   ├── api.md                         # API 文档
│   ├── deployment.md                  # 部署指南
│   └── examples.md                    # 使用示例
├── 📁 scripts/                         # 脚本工具
│   ├── setup.sh                       # 环境设置
│   └── deploy.sh                      # 部署脚本
├── docker-compose.yml                  # 容器编排
├── pyproject.toml                      # 项目配置
├── requirements.txt                    # Python 依赖
├── .env.example                        # 环境变量示例
└── README.md                          # 项目文档
```

---

## 🚀 使用场景

### 场景一：企业官网信息收集

```mermaid
sequenceDiagram
    participant User as 用户
    participant Manager as Manager Agent
    participant Leader as Information Leader
    participant Crawl4AI as Crawl4AI Employee
    participant Exa as Exa Employee

    User->>Manager: 请求收集"Tesla"公司信息
    Manager->>Leader: 分派信息收集任务
    Leader->>Crawl4AI: 爬取Tesla官网
    Crawl4AI-->>Leader: 返回结构化网站内容
    Leader->>Exa: 基于网站内容搜索补充信息
    Exa-->>Leader: 返回丰富的公司信息
    Leader-->>Manager: 整合后的完整公司档案
    Manager-->>User: 返回综合分析报告
```

### 场景二：智能降级处理

```mermaid
flowchart TD
    A[收到爬取请求] --> B[Crawl4AI 尝试爬取]
    B --> C{爬取成功?}
    C -->|是| D[返回结构化内容]
    C -->|否| E[启动降级机制]
    E --> F[Playwright 备选爬取]
    F --> G{备选成功?}
    G -->|是| H[返回基础HTML内容]
    G -->|否| I[Requests 最终尝试]
    I --> J[返回原始内容或错误]
    
    D --> K[Exa 信息补全]
    H --> K
    J --> K
    K --> L[返回最终结果]
```

---

## 🛠️ 快速开始

### 环境要求

- Python 3.11+
- Docker 20.0+
- Docker Compose 2.0+
- uv (推荐) 或 pip

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd information_collection_agent
   ```

2. **环境配置**
   ```bash
   # 复制环境变量模板
   cp .env.example .env

   # 编辑环境变量，添加必要的API密钥
   # EXA_API_KEY=your_exa_api_key_here
   ```

3. **使用 Docker Compose 启动**
   ```bash
   # 构建并启动所有服务
   docker-compose up --build -d

   # 查看服务状态
   docker-compose ps

   # 查看日志
   docker-compose logs -f
   ```

4. **验证部署**
   ```bash
   # 检查 Leader Agent Card
   curl http://localhost:8231/.well-known/agent.json

   # 检查 Crawl4AI Employee Agent Card
   curl http://localhost:8232/.well-known/agent.json

   # 检查 Exa Employee Agent Card
   curl http://localhost:8233/.well-known/agent.json
   ```

### 基本使用

```python
import asyncio
import aiohttp
import json

async def test_information_collection():
    # 向 Leader Agent 发送信息收集请求
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tasks/send",
        "params": {
            "id": "task-001",
            "message": {
                "role": "user",
                "parts": [
                    {
                        "type": "text",
                        "text": json.dumps({
                            "target_url": "https://www.tesla.com",
                            "company_name": "Tesla",
                            "priority_keywords": ["electric vehicles", "energy", "innovation"]
                        })
                    }
                ]
            },
            "metadata": {}
        }
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(
            "http://localhost:8231/a2a",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            result = await response.json()
            print(json.dumps(result, indent=2, ensure_ascii=False))

# 运行测试
asyncio.run(test_information_collection())
```

---

## 📚 API 文档

### A2A 协议端点

所有 Agent 都提供标准的 A2A 协议端点：

- **Agent Card**: `GET /.well-known/agent.json`
- **任务发送**: `POST /a2a` (method: `tasks/send`)
- **任务订阅**: `POST /a2a` (method: `tasks/sendSubscribe`)
- **任务取消**: `POST /a2a` (method: `tasks/cancel`)

### 数据格式

#### 请求格式 (JSON-RPC 2.0)

```json
{
  "jsonrpc": "2.0",
  "id": "unique-request-id",
  "method": "tasks/send",
  "params": {
    "id": "task-uuid",
    "message": {
      "role": "user",
      "parts": [
        {
          "type": "text",
          "text": "任务内容"
        }
      ]
    },
    "metadata": {}
  }
}
```

#### 响应格式

```json
{
  "jsonrpc": "2.0",
  "id": "unique-request-id",
  "result": {
    "id": "task-uuid",
    "status": {
      "state": "completed"
    },
    "artifacts": [
      {
        "name": "result",
        "parts": [
          {
            "type": "text",
            "text": "处理结果"
          }
        ]
      }
    ],
    "metadata": {}
  }
}
```

---

## 🔧 开发指南

### 本地开发环境

1. **安装依赖**
   ```bash
   # 使用 uv (推荐)
   uv pip install -r requirements.txt

   # 或使用 pip
   pip install -r requirements.txt
   ```

2. **启动开发服务**
   ```bash
   # 启动 Leader Agent
   cd agents/leader
   python main.py

   # 启动 Crawl4AI Employee (新终端)
   cd agents/employees/crawl4ai
   python main.py

   # 启动 Exa Employee (新终端)
   cd agents/employees/exa
   python main.py
   ```

3. **运行测试**
   ```bash
   # 运行所有测试
   python -m pytest tests/

   # 运行特定测试
   python -m pytest tests/test_crawl4ai.py -v
   ```

### 扩展新的 Employee

1. 创建新的 Employee 目录
2. 实现 A2A 协议接口
3. 定义 Agent Card
4. 添加到 docker-compose.yml
5. 更新 Leader 的 Employee 列表

---

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

---

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 🙏 致谢

- [Google A2A Protocol](https://developers.google.com/agent-protocol) - 标准化 Agent 通信协议
- [Crawl4AI](https://docs.crawl4ai.com/) - 强大的网页抓取引擎
- [Exa](https://exa.ai/) - AI 驱动的搜索和信息服务
- [FastMCP](https://github.com/flexflowai/fastmcp) - A2A 协议实现框架
