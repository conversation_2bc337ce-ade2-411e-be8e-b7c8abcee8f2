"""
主爬虫模块 - 整合了爬虫管理器、链接管理和状态管理
提供统一的爬取接口，支持多爬虫降级
"""

import asyncio
from collections.abc import Callable
import logging
import time
from typing import Dict, List, Any, Optional, Set, Tuple

import os
from urllib.parse import urljoin

# 导入自定义模块
from .crawler.base_crawler import BaseCrawler, CrawlResult
from .crawler_manager import CrawlerManager
from .crawler.crawl4ai_crawler import Crawl4AICrawler
from .crawler.playwright_crawler import PlaywrightCrawler
from .crawler.requests_crawler import RequestsCrawler  # 新增requests爬虫
from .crawler.selenium_crawler import SeleniumCrawler  # 新增selenium爬虫
from .crawler.drissionpage_crawler import DrissionPageCrawler  # 新增drissionpage爬虫
# 可以导入更多爬虫实现
# from .requests_crawler import RequestsCrawler
# 修改Pydantic导入 - 使用dataclasses替代BaseSettings
from dataclasses import dataclass
from ..utils.validators import is_valid_url, get_domain
from ..utils.content_cleaner import ContentCleaner
from ..utils.process_site_url import process_url_data
from .config import CrawlerConfig, CrawlerState, CrawlerHooks, retry
from ..utils.memory_monitor import memory_monitor
from ..utils.browser_pool import browser_pool
# 配置日志
logger = logging.getLogger(__name__)


class MainCrawler:
    """主爬虫类，整合链接管理、状态管理和爬虫管理器"""

    def __init__(self, config: Dict[str, Any] = None, hooks: CrawlerHooks = None):
        self.config:CrawlerConfig = CrawlerConfig(**(config or {}))
        self.state = CrawlerState()
        self.hooks = hooks or CrawlerHooks()  # 直接使用传入的hooks实例或创建新实例
        self.link_manager = None
        self.content_cleaner = ContentCleaner()
        self._crawlers_initialized = False

        # 创建爬虫管理器
        crawler_classes = self._get_crawler_classes()
        self.crawler_manager = CrawlerManager(
            crawler_classes=crawler_classes,
            config=self.config.to_dict(),
            hooks=self.hooks  # 传递hooks实例
        )

    def _get_crawler_classes(self):
        """获取爬虫类列表，按优先级排序"""
        crawlers = [
            Crawl4AICrawler,         # 尝试Crawl4AI
            DrissionPageCrawler,    # 再尝试DrissionPage
            RequestsCrawler,        # 再尝试Requests
            PlaywrightCrawler,      # 先尝试Playwright
            SeleniumCrawler,        # 再尝试Selenium
        ]
        return crawlers

    async def start(self):
        """启动爬虫"""
        if not self._crawlers_initialized:
            # 启动爬虫管理器
            await self.crawler_manager.start()
            self._crawlers_initialized = True

    async def close(self):
        """关闭爬虫"""
        if self._crawlers_initialized:
            # 关闭爬虫管理器
            await self.crawler_manager.close()
            
            # 等待一段时间让爬虫资源正常释放
            await asyncio.sleep(1)
            
            # 停止浏览器池监控
            await browser_pool.stop()
            
            self._crawlers_initialized = False

    @retry(max_attempts=3)
    async def crawl(self, url: str, is_homepage: bool = False,
                    include_external: bool = False) -> CrawlResult:
        """爬取单个URL"""
        # 确保爬虫已初始化
        await self.start()

        if not is_valid_url(url):
            self.state.update_stats(success=False)
            return CrawlResult.create_failure(
                url=url,
                error_type="InvalidURL",
                error_code="CR001",
                description="无效的URL",
                crawler_used="main"
            )

        domain = get_domain(url)

        # 获取域名锁
        domain_lock = await self.state.acquire_domain_lock(domain, self.config.max_concurrent)

        async with domain_lock:
            # 检查域名冷却
            wait_time = self.state.should_wait_domain(domain)
            if wait_time > 0:
                await asyncio.sleep(wait_time)

            # 更新域名最后爬取时间
            self.state.update_domain_last_crawl(domain)

            # 添加到活跃任务
            self.state.add_active_crawl(url)

            try:
                # 使用爬虫管理器爬取，支持降级
                result = await self.crawler_manager.crawl(
                    url=url,
                    is_homepage=is_homepage,
                    include_external=include_external
                )

                # 更新统计信息
                if result.is_success():
                    self.state.update_stats(success=True)
                else:
                    self.state.update_stats(success=False)

                    # 修复降级逻辑：如果启用降级，记录降级统计
                    if self.config.enable_fallback and result.crawler_used != "crawl4ai":
                        self.state.update_stats(fallback=True)

                return result

            finally:
                # 移除活跃任务
                self.state.remove_active_crawl(url)

    async def crawl_many(self, urls: List[str],
                         base_url: str = None) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        # 确保爬虫已初始化
        await self.start()


        # 使用信号量控制最大并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent)

        async def crawl_with_semaphore(url: str) -> Tuple[str, CrawlResult]:
            async with semaphore:
                is_homepage = (url == base_url) if base_url else False
                result = await self.crawl(url, is_homepage=is_homepage)
                return url, result

        # 使用gather而不是as_completed来实现真正的并发执行
        # 创建所有任务
        tasks = [crawl_with_semaphore(url) for url in urls]

        logger.info(f"开始并发爬取 {len(urls)} 个URLs，最大并发数: {self.config.max_concurrent}")

        # 同时执行所有任务
        try:
            # 使用gather等待所有任务完成，但允许它们同时开始执行
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            result_dict = {}
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"爬取任务异常: {str(result)}")
                else:
                    url, crawl_result = result
                    result_dict[url] = crawl_result

            return result_dict

        except Exception as e:
            logger.error(f"并发爬取过程中发生异常: {str(e)}")
            return {}

    async def domain_based_crawl_many(self, urls: List[str],
                                      base_url: str = None) -> Dict[str, CrawlResult]:
        """基于域名分组的并发爬取，提高真实并行度"""
        if not urls:
            return {}

        # 按域名分组URL
        domain_groups = {}
        for url in urls:
            domain = get_domain(url)
            if domain not in domain_groups:
                domain_groups[domain] = []
            domain_groups[domain].append(url)

        logger.info(f"URL按域名分组: {len(domain_groups)}个不同域名")

        # 为每个域名创建一个爬取任务
        async def crawl_domain_group(domain: str, domain_urls: List[str]) -> Dict[str, CrawlResult]:
            # 计算该域名的最大并发数
            domain_max_concurrent = min(
                len(domain_urls),
                self.config.max_concurrent
            )

            logger.info(
                f"开始爬取域名 {domain} 的 {len(domain_urls)} 个URLs，域名并发数: {domain_max_concurrent}")
            try:
                return await self.crawl_many(domain_urls, domain_max_concurrent, base_url)
            except Exception as e:
                logger.error(f"域名 {domain} 爬取异常: {str(e)}")
                # 创建错误结果集
                error_results = {}
                for url in domain_urls:
                    error_results[url] = CrawlResult.create_failure(
                        url=url,
                        error_type="DomainGroupError",
                        error_code="DG001",
                        description=f"域名组爬取异常: {str(e)}",
                        crawler_used="main"
                    )
                return error_results

        # 为每个域名分组创建任务
        domain_tasks = [
            crawl_domain_group(domain, urls)
            for domain, urls in domain_groups.items()
        ]

        # 同时爬取所有域名分组
        all_results = {}
        domain_results = await asyncio.gather(*domain_tasks, return_exceptions=True)

        # 合并所有域名的结果
        for result in domain_results:
            if isinstance(result, Exception):
                logger.error(f"域名组爬取异常: {str(result)}")
            elif isinstance(result, dict):
                all_results.update(result)

        return all_results

    async def crawl_site(self, base_url: str, priority_keywords: List[str] = None, company_name:str = None) -> Dict[str, CrawlResult]:
        """爬取整个网站 - 使用爬虫管理器的能力"""
        # 参数验证和处理
        if not is_valid_url(base_url):
            return {
                base_url: CrawlResult.create_failure(
                    url=base_url,
                    error_type="InvalidURL",
                    error_code="MC001",
                    description="无效的URL",
                    crawler_used="main"
                )
            }
        if not company_name:
            raise ValueError("company_name is required")

        # 记录爬取开始时间
        start_time = time.time()
        logger.info(
            f"开始爬取网站: {base_url}, 最大URL数: {self.config.max_urls}, 包含外链: {self.config.include_external}, 最大深度: {self.config.max_depth}")

        try:
            # 确保爬虫已启动
            await self.start()

            # 构建爬取参数
            crawl_params = {
                "max_urls": self.config.max_urls,   
                "include_external": self.config.include_external,
                "max_concurrent":self.config.max_concurrent,
                "max_depth": self.config.max_depth,
                "handle_failed_urls": self.config.handle_failed_urls  # 添加失败URL处理参数
            }

            # 如果有优先关键字，添加到参数中
            if priority_keywords:
                crawl_params["priority_keywords"] = priority_keywords

            # 使用爬虫管理器进行网站爬取 - 利用现有爬虫的能力和降级机制
            results = await self.crawler_manager.crawl_site(base_url, **crawl_params)
            
            if not results:
                logger.warning(f"爬取网站 {base_url} 未返回任何结果")
                return {}
            
            self.hooks.on_crawl_success(base_url, results)
            
            # 记录爬取完成
            end_time = time.time()
            elapsed = end_time - start_time

            # 更新统计信息
            results_list = list(results.values()) if isinstance(results, dict) else []
            successful_urls = sum(1 for result in results_list if hasattr(result, 'is_success') and result.is_success())

            # 获取失败URL的统计信息
            failed_urls_count = len(results_list) - successful_urls
            recovered_urls_count = 0
            logger.info(f"网站爬取完成，共获取 {len(results_list)} 个URL，成功 {successful_urls} 个，" +
                        f"失败 {failed_urls_count} 个，恢复 {recovered_urls_count} 个，耗时 {elapsed:.2f}秒")
            
            # ===== 修复：安全地处理和转换结果 =====
            
            # 创建结果的副本，防止在处理过程中修改原始数据
            processed_results = {}
            
            # 添加公司名称到所有item中的company_name字段
            try:
                for key, result in results.items():
                    # 检查result是否为CrawlResult对象
                    if hasattr(result, 'to_dict'):
                        result_dict = result.to_dict()
                        result_dict['company_name'] = company_name
                        processed_results[str(key)] = result_dict
                    else:
                        # 已经是字典或其他类型
                        processed_item = result.copy() if isinstance(result, dict) else {'content': str(result)}
                        processed_item['company_name'] = company_name
                        processed_results[str(key)] = processed_item
                        
                # 使用process_url_data处理，但确保数据格式正确
                format_results = process_url_data(processed_results)
                
                # 清理内容
                cleaned_results = {}
                for key, content in format_results.items():
                    if not isinstance(content, dict) or 'children' not in content or len(content.get('children', [])) == 0:
                        cleaned_results[key] = content
                        continue
                    
                    try:
                        cleaned_content = self.content_cleaner.process_json_data(content)
                        cleaned_results[key] = cleaned_content
                    except Exception as e:
                        logger.error(f"清理内容时出错: {str(e)}")
                        cleaned_results[key] = content
                
                return cleaned_results
                
            except Exception as e:
                logger.error(f"处理爬取结果时出错: {str(e)}")
                # 如果处理失败，返回简化的结果
                simple_results = {}
                for key, result in results.items():
                    if hasattr(result, 'is_success') and result.is_success():
                        simple_results[str(key)] = {
                            'url': str(key),
                            'status': 'success',
                            'company_name': company_name,
                            'content': result.content if hasattr(result, 'content') else 'Content unavailable'
                        }
                    else:
                        simple_results[str(key)] = {
                            'url': str(key),
                            'status': 'failed',
                            'company_name': company_name,
                            'error': str(result.error) if hasattr(result, 'error') else 'Unknown error'
                        }
                return simple_results

        except Exception as e:
            # 处理顶层异常
            logger.error(f"网站爬取过程中发生异常: {str(e)}")
            self.hooks.on_crawl_failure(base_url, e)
            return {
                "error": str(e),
                "base_url": base_url,
                "company_name": company_name
            }
        finally:
            # 如果没有其他活跃任务，清理资源
            if not self.state.active_crawls:
                try:
                    await self.close()
                except Exception as close_error:
                    logger.error(f"关闭爬虫资源时出错: {str(close_error)}")

    @retry(max_attempts=3)
    async def crawl_multiple_sites(self, base_urls: List[str],
                                   max_concurrent_sites: int = 5) -> Dict[str, Dict[str, CrawlResult]]:
        """并发爬取多个网站

        Args:
            base_urls: 要爬取的网站基础URL列表
            max_concurrent_sites: 同时爬取的最大网站数量

        Returns:
            以网站URL为键，爬取结果为值的字典
        """
        # 参数验证
        if not base_urls:
            return {}

        # 过滤无效URL
        valid_urls = [url for url in base_urls if is_valid_url(url)]
        if len(valid_urls) < len(base_urls):
            logger.warning(f"过滤了 {len(base_urls) - len(valid_urls)} 个无效URL")

        if not valid_urls:
            logger.error("没有有效的URL可爬取")
            return {}


        # 限制并发网站数量，避免资源耗尽
        max_concurrent_sites = min(max_concurrent_sites, 10)

        # 记录爬取开始时间
        start_time = time.time()
        logger.info(
            f"开始并发爬取 {len(valid_urls)} 个网站，并发数: {max_concurrent_sites}，每网站最大URL数: {self.config.max_urls}")

        try:
            # 确保爬虫已启动
            await self.start()

            # 构建爬取参数
            crawl_params = {
                "max_urls": self.config.max_urls,
                "include_external": self.config.include_external,
                "max_concurrent": self.config.max_concurrent,
                "max_depth": self.config.max_depth
            }

            # 如果网站数量大于并发限制，分批处理
            if len(valid_urls) > max_concurrent_sites:
                logger.info(
                    f"网站数量({len(valid_urls)})超过并发限制({max_concurrent_sites})，将分批处理")

                all_results = {}
                for i in range(0, len(valid_urls), max_concurrent_sites):
                    batch_urls = valid_urls[i:i+max_concurrent_sites]
                    logger.info(
                        f"开始处理第 {i//max_concurrent_sites + 1} 批，{len(batch_urls)} 个网站")

                    batch_results = await self.crawler_manager.crawl_multiple_sites(batch_urls, **crawl_params)
                    all_results.update(batch_results)

                    # 短暂暂停，避免连续批次导致过高资源使用
                    if i + max_concurrent_sites < len(valid_urls):
                        await asyncio.sleep(1)

                results = all_results
            else:
                # 直接调用爬虫管理器的多网站爬取方法
                results = await self.crawler_manager.crawl_multiple_sites(valid_urls, **crawl_params)

            # 计算总体统计
            end_time = time.time()
            elapsed = end_time - start_time

            total_pages = sum(
                len(site_result)
                for site_result in results.values()
            )

            logger.info(
                f"所有网站爬取完成，共爬取 {len(results)} 个网站，{total_pages} 个页面，总耗时 {elapsed:.2f}秒")
            # format_results = {str(result.url): result.to_dict() for result in results.values()}
            return results

        except Exception as e:
            # 处理顶层异常
            logger.error(f"批量爬取网站过程中发生异常: {str(e)}")
            return {"__error__": str(e)}

    async def crawl_site_with_specific_crawler(self, base_url: str, crawler_name: str,
                                               **kwargs) -> Dict[str, CrawlResult]:
        """使用指定爬虫爬取网站，不使用降级机制

        Args:
            base_url: 网站基础URL
            crawler_name: 爬虫类名，如'Crawl4AICrawler'、'PlaywrightCrawler'等
            **kwargs: 传递给爬虫的参数

        Returns:
            爬取结果字典
        """
        if not is_valid_url(base_url):
            return {
                base_url: CrawlResult.create_failure(
                    url=base_url,
                    error_type="InvalidURL",
                    error_code="SPC001",
                    description="无效的URL",
                    crawler_used="main"
                )
            }

        # 确保爬虫已启动
        await self.start()

        # 查找指定爬虫
        target_crawler = None
        for crawler in self.crawler_manager.crawlers:
            if type(crawler).__name__ == crawler_name:
                target_crawler = crawler
                break

        if target_crawler is None:
            logger.error(f"未找到指定的爬虫: {crawler_name}")
            return {
                base_url: CrawlResult.create_failure(
                    url=base_url,
                    error_type="CrawlerNotFound",
                    error_code="SPC002",
                    description=f"未找到指定的爬虫: {crawler_name}",
                    crawler_used="main"
                )
            }

        # 记录开始时间
        start_time = time.time()
        logger.info(f"使用 {crawler_name} 爬取网站: {base_url}")

        try:
            # 直接调用特定爬虫的crawl_site方法
            results = await target_crawler.crawl_site(base_url, **kwargs)

            # 记录结束时间和统计信息
            end_time = time.time()
            elapsed = end_time - start_time
            logger.info(
                f"使用 {crawler_name} 爬取网站完成，获取 {len(results) - 1} 个URL，耗时 {elapsed:.2f}秒")
            return results

        except Exception as e:
            logger.error(f"使用 {crawler_name} 爬取网站时出错: {str(e)}")

            # 返回错误结果
            return {
                base_url: CrawlResult.create_failure(
                    url=base_url,
                    error_type="SpecificCrawlerError",
                    error_code="SPC003",
                    description=f"使用 {crawler_name} 爬取出错: {str(e)}",
                    crawler_used=crawler_name
                ),
            }
