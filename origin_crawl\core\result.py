"""
爬取结果处理模块，定义统一的结果格式和处理方法
"""
import json
import datetime
from dataclasses import dataclass, field, asdict
from typing import Dict, Optional, Any, List

@dataclass
class CrawlResultError:
    """爬取错误信息"""
    error_type: str
    error_code: str
    description: str
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典格式"""
        return {
            "error_type": self.error_type,
            "error_code": self.error_code,
            "description": self.description
        }

@dataclass
class CrawlResult:
    """统一的爬取结果格式"""
    url: str
    status: str  # "success" or "failed"
    timestamp: str = field(default_factory=lambda: datetime.datetime.now().isoformat())
    content: Any = None  # 处理后的内容，默认为markdown
    html: Optional[str] = None  # 新增：原始HTML内容
    raw_html: Optional[str] = None  # 新增：原始HTML内容
    format: str = "markdown"
    error: Optional[CrawlResultError] = None
    crawler_used: str = "crawl4ai"  # 使用的爬虫技术
    metadata: Dict[str, Any] = field(default_factory=dict)
    filtered_urls: Dict[str, List[str]] = field(default_factory=lambda: {"pdf": [], "filtered": [], "documents": []})
    links: Any = field(default_factory=list)  # 可以是列表或字典格式
    source: str = "official_website"  # 新增：来源
    company_name: str = None  # 新增：公司名称

    def __post_init__(self):
        """初始化后处理"""
        # 确保时间戳格式正确
        if not isinstance(self.timestamp, str):
            self.timestamp = self.timestamp.isoformat()
        
        # 统一链接格式
        self._normalize_links()
    
    def _normalize_links(self):
        """统一链接格式
        将复杂的链接结构转换为简单的链接列表 [{'href': '...', 'text': '...'}, ...]
        """
        # 如果links已经是字典格式(如{internal: [...], external: [...]})，则提取所有链接
        if isinstance(self.links, dict):
            normalized_links = []
            for category, links in self.links.items():
                if isinstance(links, list):
                    normalized_links.extend(links)
            self.links = normalized_links
                
    # 添加获取所有链接的辅助方法
    def get_link_hrefs(self) -> List[str]:
        """获取所有链接的href属性列表"""
        if not self.links:
            return []
            
        hrefs = []
        for link in self.links:
            if isinstance(link, dict) and 'href' in link:
                hrefs.append(link['href'])
            elif isinstance(link, str):
                hrefs.append(link)
        return hrefs
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "url": self.url,
            "status": self.status,
            "timestamp": self.timestamp,
            "content": self.content,
            "format": self.format,
            "crawler_used": self.crawler_used,
            "filtered_urls": self.filtered_urls,
            # "links": self.links,
            # "raw_html": self.raw_html,
            "html": self.html,
            "source": self.source,
            "company_name": self.company_name
        }
        
        # 添加HTML内容(如果存在)
        if self.html:
            result["html"] = self.html
            
        # 添加元数据
        if self.metadata:
            result["metadata"] = self.metadata
            
        # 添加错误信息
        if self.error:
            result["error"] = self.error.to_dict()
            
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def create_success(cls, url: str, content: Any, format: str = "markdown", 
                      crawler_used: str = "crawl4ai", metadata: Dict[str, Any] = None,
                      html: Optional[str] = None) -> 'CrawlResult':
        """创建成功的爬取结果"""
        return cls(
            url=url,
            status="success",
            content=content,
            html=html,  # 添加HTML内容参数
            format=format,
            crawler_used=crawler_used,
            metadata=metadata or {}
        )
    
    @classmethod
    def create_failure(cls, url: str, error_type: str, error_code: str, 
                      description: str, crawler_used: str = "crawl4ai") -> 'CrawlResult':
        """创建失败的爬取结果"""
        error = CrawlResultError(
            error_type=error_type,
            error_code=error_code,
            description=description
        )
        
        return cls(
            url=url,
            status="failed",
            error=error,
            crawler_used=crawler_used
        )
    
    def is_success(self) -> bool:
        """检查爬取是否成功"""
        return self.status == "success"
    
    def is_failure(self) -> bool:
        """检查爬取是否失败"""
        return self.status == "failed"
    
    @property
    def markdown(self) -> str:
        """获取markdown内容，保持与Crawl4AI兼容"""
        return self.content if self.format == "markdown" else ""
    
    # 添加从Crawl4AI结果转换为自定义结果的方法
    @classmethod
    def from_crawl4ai_result(cls, crawl4ai_result) -> 'CrawlResult':
        """从Crawl4AI的结果对象创建CrawlResult实例"""
        if not crawl4ai_result.success:
            return cls.create_failure(
                url=crawl4ai_result.url,
                error_type="CrawlError",
                error_code="CR004",
                description=crawl4ai_result.error_message or "未知错误",
                crawler_used="crawl4ai"
            )
        
        # 提取markdown内容
        # markdown_content = ""
        # if hasattr(crawl4ai_result, 'markdown'):
        #     if hasattr(crawl4ai_result.markdown, 'fit_markdown') and crawl4ai_result.markdown.fit_markdown:
        #         markdown_content = crawl4ai_result.markdown.fit_markdown
        #     else:
        #         markdown_content = str(crawl4ai_result.markdown)
        
        # 获取原始HTML(如果存在)
        html_content = None
        if hasattr(crawl4ai_result, 'html'):
            html_content = crawl4ai_result.html
        
        # 创建成功的结果
        result = cls.create_success(
            url=crawl4ai_result.url,
            content=crawl4ai_result.markdown,
            html=html_content,  # 添加HTML内容
            format="markdown",
            crawler_used="crawl4ai",
            metadata=crawl4ai_result.metadata or {}
        )
        
        # 处理链接 - 支持不同格式
        if hasattr(crawl4ai_result, 'links'):
            result.links = crawl4ai_result.links
            result._normalize_links()  # 确保链接格式统一
        
        return result 