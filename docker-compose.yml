version: '3.8'

services:
  # Information Leader Agent
  info-leader:
    build:
      context: .
      dockerfile: agents/leader/Dockerfile
    container_name: ${LEADER_CONTAINER_NAME:-info-leader}
    ports:
      - "${LEADER_PORT:-8231}:8231"
    environment:
      - HOST=${LEADER_HOST:-0.0.0.0}
      - PORT=8231
      - LOG_LEVEL=${LEADER_LOG_LEVEL:-INFO}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
    volumes:
      - ./logs:/app/logs
    networks:
      - information-collection
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8231/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
    restart: unless-stopped
    depends_on:
      - crawl4ai-employee

  # Crawl4AI Employee Agent
  crawl4ai-employee:
    build:
      context: .
      dockerfile: agents/employees/crawl4ai/Dockerfile
    container_name: ${CRAWL4AI_CONTAINER_NAME:-crawl4ai-employee}
    ports:
      - "${CRAWL4AI_PORT:-8232}:8232"
    environment:
      - HOST=${CRAWL4AI_HOST:-0.0.0.0}
      - PORT=8232
      - LOG_LEVEL=${CRAWL4AI_LOG_LEVEL:-INFO}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - CRAWL4AI_TIMEOUT=${CRAWL4AI_TIMEOUT:-30}
      - CRAWL4AI_MAX_CONCURRENT=${CRAWL4AI_MAX_CONCURRENT:-5}
      - CRAWL4AI_ENABLE_FALLBACK=${CRAWL4AI_ENABLE_FALLBACK:-true}
      - CRAWL4AI_FALLBACK_TIMEOUT=${CRAWL4AI_FALLBACK_TIMEOUT:-15}
      - CRAWL4AI_USER_AGENT=${CRAWL4AI_USER_AGENT}
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage
    networks:
      - information-collection
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8232/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-512m}
        reservations:
          memory: ${MEMORY_RESERVATION:-256m}

  # Exa Employee Agent (optional)
  exa-employee:
    build:
      context: .
      dockerfile: agents/employees/exa/Dockerfile
    container_name: ${EXA_CONTAINER_NAME:-exa-employee}
    ports:
      - "${EXA_PORT:-8233}:8233"
    environment:
      - HOST=${EXA_HOST:-0.0.0.0}
      - PORT=8233
      - LOG_LEVEL=${EXA_LOG_LEVEL:-INFO}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - EXA_API_KEY=${EXA_API_KEY}
    volumes:
      - ./logs:/app/logs
    networks:
      - information-collection
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8233/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
    restart: unless-stopped
    profiles:
      - exa  # Only start when 'exa' profile is specified

  # Redis (optional, for caching and task queue)
  redis:
    image: redis:7-alpine
    container_name: information-collection-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - information-collection
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    profiles:
      - redis  # Only start when 'redis' profile is specified

  # Nginx (optional, for load balancing and reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: information-collection-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - information-collection
    depends_on:
      - info-leader
      - crawl4ai-employee
    restart: unless-stopped
    profiles:
      - nginx  # Only start when 'nginx' profile is specified

networks:
  information-collection:
    name: ${NETWORK_NAME:-information-collection-network}
    driver: bridge

volumes:
  redis_data:
    driver: local
