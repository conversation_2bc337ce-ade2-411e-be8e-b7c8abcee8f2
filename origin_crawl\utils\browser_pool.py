"""
浏览器进程池管理器 - 优雅地管理Chrome实例
"""
import asyncio
import logging
import time
import os
import psutil
import signal
import weakref
from typing import Dict, List, Set, Optional, Any
import threading
import atexit

logger = logging.getLogger(__name__)

class BrowserInstance:
    """浏览器实例记录"""
    def __init__(self, pid: int, instance_id: str, browser_type: str):
        self.pid = pid
        self.id = instance_id
        self.type = browser_type
        self.create_time = time.time()
        self.last_used = time.time()
        self.ref_count = 1
        self.marked_for_close = False
    
    def add_ref(self):
        """增加引用计数"""
        self.ref_count += 1
        self.last_used = time.time()
    
    def release(self):
        """减少引用计数"""
        self.ref_count -= 1
        if self.ref_count < 0:
            self.ref_count = 0
        return self.ref_count == 0
    
    def is_idle(self, idle_threshold=300):
        """检查浏览器是否空闲"""
        return self.ref_count == 0 and (time.time() - self.last_used > idle_threshold)
    
    def age(self):
        """获取浏览器实例的存活时间(秒)"""
        return time.time() - self.create_time
    
    def idle_time(self):
        """获取浏览器实例的空闲时间(秒)"""
        return time.time() - self.last_used


class BrowserPool:
    """浏览器进程池管理器"""
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = BrowserPool()
        return cls._instance
    
    def __init__(self):
        """初始化浏览器池"""
        self.browsers = {}  # pid -> BrowserInstance
        self.browser_ids = {}  # instance_id -> pid
        self.cleanup_task = None
        self.running = False
        self._cleanup_lock = asyncio.Lock()
        self.max_instances = 10  # 最大浏览器实例数
        self.idle_timeout = 300  # 空闲浏览器超时时间(秒)
        self.check_interval = 60  # 检查间隔(秒)
        self.last_check_time = 0
        
        # 注册进程退出时的清理
        atexit.register(self._cleanup_on_exit)
    
    def _cleanup_on_exit(self):
        """程序退出时清理"""
        logger.info(f"程序退出，清理所有浏览器进程 ({len(self.browsers)}个)")
        for pid in list(self.browsers.keys()):
            try:
                self._terminate_process(pid)
            except:
                pass
    
    def register_browser(self, pid: int, instance_id: str, browser_type: str = "unknown") -> BrowserInstance:
        """注册浏览器进程"""
        if pid in self.browsers:
            # 已存在的浏览器增加引用计数
            browser = self.browsers[pid]
            browser.add_ref()
            logger.debug(f"增加浏览器 {pid} ({browser_type}) 引用计数: {browser.ref_count}")
            return browser
        
        # 创建新的浏览器记录
        browser = BrowserInstance(pid, instance_id, browser_type)
        self.browsers[pid] = browser
        self.browser_ids[instance_id] = pid
        logger.debug(f"注册新浏览器: {pid} ({browser_type}), ID: {instance_id}")
        
        # 确保清理任务已启动
        self._ensure_cleanup_task()
        
        return browser
    
    def release_browser(self, browser_id: str) -> bool:
        """释放浏览器引用"""
        pid = self.browser_ids.get(browser_id)
        if not pid or pid not in self.browsers:
            return False
        
        browser = self.browsers[pid]
        is_zero = browser.release()
        logger.debug(f"释放浏览器 {pid} ({browser.type}) 引用, 当前计数: {browser.ref_count}")
        
        # 不立即关闭，让清理任务处理空闲浏览器
        return is_zero
    
    def get_browser_info(self, browser_id: str) -> Optional[BrowserInstance]:
        """获取浏览器信息"""
        pid = self.browser_ids.get(browser_id)
        if not pid:
            return None
        return self.browsers.get(pid)
    
    def _ensure_cleanup_task(self):
        """确保清理任务已启动"""
        if not self.running and self.cleanup_task is None:
            self.running = True
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def stop(self):
        """停止浏览器池和清理任务"""
        self.running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
    
    async def _cleanup_loop(self):
        """清理循环"""
        logger.info("浏览器池清理任务已启动")
        while self.running:
            try:
                await self._check_and_cleanup()
            except Exception as e:
                logger.error(f"浏览器池清理任务出错: {e}")
            
            # 等待下一次检查
            await asyncio.sleep(self.check_interval)
    
    async def _check_and_cleanup(self):
        """检查并清理空闲浏览器"""
        async with self._cleanup_lock:
            now = time.time()
            # 限制检查频率
            if now - self.last_check_time < self.check_interval:
                return
            
            self.last_check_time = now
            logger.debug(f"检查浏览器池，当前有 {len(self.browsers)} 个浏览器实例")
            
            # 检查进程是否仍然存在
            self._check_processes_exist()
            
            # 查找并关闭空闲浏览器
            idle_browsers = [
                (pid, browser) for pid, browser in self.browsers.items()
                if browser.is_idle(self.idle_timeout)
            ]
            
            # 关闭超时的空闲浏览器
            for pid, browser in idle_browsers:
                if browser.idle_time() > self.idle_timeout:
                    logger.info(f"关闭空闲浏览器: {pid} ({browser.type}), 空闲时间: {browser.idle_time():.1f}秒")
                    await self._close_browser(pid, browser.id)
            
            # 如果浏览器实例太多，关闭一些最不活跃的实例
            active_browsers = [(pid, browser) for pid, browser in self.browsers.items() 
                              if browser.ref_count == 0 and not browser.marked_for_close]
            
            if len(self.browsers) > self.max_instances and active_browsers:
                # 按最后使用时间排序
                active_browsers.sort(key=lambda x: x[1].last_used)
                
                # 关闭最早不活跃的浏览器
                excess_count = len(self.browsers) - self.max_instances
                for pid, browser in active_browsers[:excess_count]:
                    logger.info(f"关闭多余浏览器: {pid} ({browser.type}), 最后使用: {browser.idle_time():.1f}秒前")
                    await self._close_browser(pid, browser.id)
    
    def _check_processes_exist(self):
        """检查进程是否仍然存在"""
        for pid in list(self.browsers.keys()):
            try:
                # 检查进程是否存在
                process = psutil.Process(pid)
                if not process.is_running() or process.status() == 'zombie':
                    logger.warning(f"浏览器进程 {pid} 不存在或已变为僵尸进程，移除记录")
                    self._remove_browser_record(pid)
            except psutil.NoSuchProcess:
                logger.warning(f"浏览器进程 {pid} 不存在，移除记录")
                self._remove_browser_record(pid)
            except Exception as e:
                logger.error(f"检查浏览器进程 {pid} 时出错: {e}")
    
    async def _close_browser(self, pid: int, browser_id: str):
        """关闭浏览器进程"""
        if pid not in self.browsers:
            return
        
        browser = self.browsers[pid]
        if browser.marked_for_close:
            return
        
        # 标记为准备关闭，避免重复关闭
        browser.marked_for_close = True
        
        try:
            # 温和地终止进程
            self._terminate_process(pid)
        except Exception as e:
            logger.error(f"关闭浏览器 {pid} 失败: {e}")
        finally:
            # 移除记录
            self._remove_browser_record(pid)
    
    def _terminate_process(self, pid: int):
        """终止进程"""
        try:
            process = psutil.Process(pid)
            process.terminate()
            
            # 给进程一些时间来优雅地关闭
            gone, still_alive = psutil.wait_procs([process], timeout=3)
            
            # 如果进程仍然存活，强制杀死
            if still_alive:
                for p in still_alive:
                    p.kill()
        except psutil.NoSuchProcess:
            # 进程已经不存在
            pass
        except Exception as e:
            logger.error(f"终止进程 {pid} 时出错: {e}")
    
    def _remove_browser_record(self, pid: int):
        """移除浏览器记录"""
        if pid in self.browsers:
            browser = self.browsers.pop(pid)
            if browser.id in self.browser_ids:
                self.browser_ids.pop(browser.id)
    
    def clean_dangling_chrome_processes(self, force=False):
        """清理可能的残留Chrome进程"""
        try:
            current_process = psutil.Process(os.getpid())
            chrome_processes = []
            
            # 找出所有Chrome子进程
            for proc in current_process.children(recursive=True):
                try:
                    if proc.is_running() and ("chrome" in proc.name().lower() or "chromium" in proc.name().lower()):
                        # 检查是否是我们已知的浏览器
                        if proc.pid in self.browsers:
                            # 跳过已注册的浏览器
                            continue
                        
                        # 收集潜在的悬空Chrome进程
                        chrome_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if chrome_processes:
                logger.warning(f"发现 {len(chrome_processes)} 个未注册的Chrome进程")
                
                if force:
                    # 强制模式：关闭所有未注册的Chrome进程
                    for proc in chrome_processes:
                        try:
                            proc.terminate()
                            logger.info(f"已终止未注册的Chrome进程: {proc.pid}")
                        except:
                            pass
                else:
                    # 温和模式：只关闭运行超过30分钟的进程
                    now = time.time()
                    for proc in chrome_processes:
                        try:
                            proc_age = now - proc.create_time()
                            if proc_age > 1800:  # 30分钟
                                proc.terminate()
                                logger.info(f"已终止长时间运行的Chrome进程: {proc.pid}, 运行时间: {proc_age:.1f}秒")
                        except:
                            pass
        except Exception as e:
            logger.error(f"清理Chrome进程时出错: {e}")

# 创建全局单例实例
browser_pool = BrowserPool.get_instance() 