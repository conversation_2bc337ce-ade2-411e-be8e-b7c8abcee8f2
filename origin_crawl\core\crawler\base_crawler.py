"""
基础爬虫抽象类，定义爬虫接口和通用功能
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
import asyncio
import inspect
import logging
import uuid
import time

# 导入CrawlResult
from ..result import CrawlResult
from ..config import CrawlerHooks
from ...utils.browser_manager import browser_manager

logger = logging.getLogger(__name__)

class BaseCrawler(ABC):
    """爬虫基类"""
    def __init__(self, config: Dict[str, Any] = None, hooks: CrawlerHooks = None):
        self.config = config or {}
        self._crawler = None
        self.hooks = hooks or CrawlerHooks()
        self._browser_pids = set()  # 存储浏览器进程PID
        
    @abstractmethod
    async def start(self):
        """初始化爬虫实例"""
        pass
        
    @abstractmethod
    async def close(self):
        """关闭爬虫实例"""
        # 清理记录的所有浏览器进程
        for pid in list(self._browser_pids):
            browser_manager.unregister_process(pid)
        self._browser_pids.clear()
    
    @abstractmethod
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """爬取单个URL"""
        # 爬取开始时钩子
        await self.hooks.execute_hook("on_crawl_start", url)
        try:
            # 具体爬取逻辑由子类实现
            result = await self._crawl_impl(url, **kwargs)
            # 爬取成功时钩子
            if result.is_success():
                await self.hooks.execute_hook("on_crawl_success", url, result)
            else:
                error = result.error if hasattr(result, 'error') else Exception("未知错误")
                await self.hooks.execute_hook("on_crawl_failure", url, error)
            return result
        except Exception as e:
            # 爬取失败时钩子
            await self.hooks.execute_hook("on_crawl_failure", url, e)
            raise e
    
    @abstractmethod
    async def _crawl_impl(self, url: str, **kwargs) -> CrawlResult:
        """实际的爬取实现，由子类重写"""
        pass
        
    @abstractmethod
    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        pass
        
    @abstractmethod
    async def crawl_site(self, base_url: str, **kwargs) -> Dict[str, CrawlResult]:
        """爬取整个网站"""
        pass
    
    async def process_collected_urls(self, url: str, sub_urls: List[str]) -> List[str]:
        """处理收集到的子链接，应用钩子"""
        try:
            processed_urls = await self.hooks.execute_hook("on_collect_sub_url", url, sub_urls)
            return processed_urls if processed_urls is not None else sub_urls
        except Exception as e:
            await self.hooks.execute_hook("on_collect_sub_url_failure", url, sub_urls, e)
            return sub_urls
        
    async def process_pdf_file(self, url: str) -> Any:
        """处理PDF文件，应用钩子"""
        try:
            processed_pdf = await self.hooks.execute_hook("parse_pdf_file", url, CrawlResult)
            return processed_pdf
        except Exception as e:
            await self.hooks.execute_hook("parse_pdf_file_failure", url, CrawlResult, e)
            return CrawlResult.create_success(
                url=url,
                content='crawler system is not suitable for processing PDF files',
                crawler_used="crawl4ai",
            )
    
    async def execute_hook(self, hook_name: str, *args, **kwargs):
        """执行钩子函数"""
        return await self.hooks.execute_hook(hook_name, *args, **kwargs)
    
    def register_browser_process(self, pid: int):
        """注册浏览器进程"""
        if pid and pid > 0:
            browser_manager.register_process(pid)
            self._browser_pids.add(pid)
            logger.debug(f"{self.__class__.__name__} 注册Chrome进程: {pid}")

