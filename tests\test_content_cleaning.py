"""
内容清洗功能测试
"""
import pytest
from unittest.mock import Mock, patch

from common.models import CrawlOptions, Crawl4AIConfig
from agents.employees.crawl4ai.crawler import SmartCrawlerCore


class TestContentCleaning:
    """内容清洗功能测试"""
    
    @pytest.fixture
    def config(self):
        return Crawl4AIConfig(
            timeout=10,
            content_cleaning=True,
            extract_links=True
        )
    
    @pytest.fixture
    def crawler_core(self, config):
        return SmartCrawlerCore(config)
    
    def test_content_cleaner_initialization(self, crawler_core):
        """测试内容清洗器初始化"""
        assert crawler_core.content_cleaner is not None
        assert hasattr(crawler_core.content_cleaner, 'clean_html_content')
        assert hasattr(crawler_core.content_cleaner, 'process_page_content')
    
    @pytest.mark.asyncio
    async def test_crawl4ai_with_content_cleaning(self, crawler_core):
        """测试Crawl4AI结果的内容清洗"""
        # Mock Crawl4AI结果
        mock_result = Mock()
        mock_result.html = """
        <html>
        <head><title>Test Page</title></head>
        <body>
            <header>Navigation</header>
            <main>
                <h1>Main Content</h1>
                <p>This is the main content of the page.</p>
            </main>
            <footer>Footer content</footer>
            <script>console.log('test');</script>
        </body>
        </html>
        """
        mock_result.markdown = "# Test Page\n\nThis is markdown content"
        mock_result.links = []
        mock_result.title = "Test Page"
        mock_result.description = "Test description"
        
        # Mock内容清洗器
        with patch.object(crawler_core.content_cleaner, 'process_page_content') as mock_clean:
            mock_clean.return_value = {
                "cleaned_content": "# Main Content\n\nThis is the main content of the page.",
                "cleaned_html": "<html><body><h1>Main Content</h1><p>This is the main content of the page.</p></body></html>"
            }
            
            result = crawler_core._process_crawl4ai_result("https://example.com", mock_result)
            
            assert result.success is True
            assert result.content == "# Main Content\n\nThis is the main content of the page."
            assert "Main Content" in result.html
            assert result.metadata["content_cleaned"] is True
            assert "original_content_length" in result.metadata
            assert "cleaned_content_length" in result.metadata
            
            # 验证内容清洗器被调用
            mock_clean.assert_called_once_with(mock_result.html)
    
    @pytest.mark.asyncio
    async def test_content_cleaning_disabled(self, crawler_core):
        """测试禁用内容清洗的情况"""
        # 禁用内容清洗
        crawler_core.config.content_cleaning = False
        
        mock_result = Mock()
        mock_result.html = "<html><body><p>Original content</p></body></html>"
        mock_result.markdown = "Original markdown content"
        mock_result.links = []
        mock_result.title = "Test"
        mock_result.description = ""
        
        result = crawler_core._process_crawl4ai_result("https://example.com", mock_result)
        
        assert result.success is True
        assert result.content == "Original markdown content"
        assert result.metadata["content_cleaned"] is False
    
    @pytest.mark.asyncio
    async def test_content_cleaning_error_handling(self, crawler_core):
        """测试内容清洗错误处理"""
        mock_result = Mock()
        mock_result.html = "<html><body><p>Test content</p></body></html>"
        mock_result.markdown = "Fallback markdown"
        mock_result.links = []
        mock_result.title = "Test"
        mock_result.description = ""
        
        # Mock内容清洗器抛出异常
        with patch.object(crawler_core.content_cleaner, 'process_page_content') as mock_clean:
            mock_clean.side_effect = Exception("Cleaning failed")
            
            result = crawler_core._process_crawl4ai_result("https://example.com", mock_result)
            
            assert result.success is True
            assert result.content == "Fallback markdown"  # 应该回退到原始markdown
            assert result.metadata["content_cleaned"] is True  # 配置仍然是启用的
    
    def test_html_cleaning_functionality(self, crawler_core):
        """测试HTML清洗功能"""
        dirty_html = """
        <html>
        <head>
            <title>Test Page</title>
            <script>alert('test');</script>
            <style>body { color: red; }</style>
        </head>
        <body>
            <header>Navigation Menu</header>
            <nav>
                <ul>
                    <li><a href="/home">Home</a></li>
                    <li><a href="/about">About</a></li>
                </ul>
            </nav>
            <main>
                <h1>Main Title</h1>
                <p>This is the main content that should be preserved.</p>
                <div class="sidebar">Sidebar content</div>
            </main>
            <footer>
                <p>Copyright 2024</p>
            </footer>
        </body>
        </html>
        """
        
        # 测试HTML清洗
        cleaned_html = crawler_core.content_cleaner.clean_html_content(dirty_html)
        
        # 验证清洗结果
        assert cleaned_html is not None
        assert len(cleaned_html) < len(dirty_html)  # 清洗后应该更短
        
        # 验证有害内容被移除
        assert "<script>" not in cleaned_html
        assert "<style>" not in cleaned_html
        assert "<nav>" not in cleaned_html
        assert "<header>" not in cleaned_html
        assert "<footer>" not in cleaned_html
        
        # 验证有用内容被保留
        assert "Main Title" in cleaned_html or "This is the main content" in cleaned_html
    
    def test_markdown_extraction(self, crawler_core):
        """测试Markdown提取功能"""
        html_content = """
        <html>
        <body>
            <h1>Main Title</h1>
            <p>This is a paragraph with <strong>bold text</strong>.</p>
            <h2>Subtitle</h2>
            <ul>
                <li>List item 1</li>
                <li>List item 2</li>
            </ul>
        </body>
        </html>
        """
        
        # 测试页面内容处理
        result = crawler_core.content_cleaner.process_page_content(html_content)
        
        assert result is not None
        assert "cleaned_content" in result
        assert "cleaned_html" in result
        
        cleaned_content = result["cleaned_content"]
        if cleaned_content:
            # 验证Markdown格式
            assert "Main Title" in cleaned_content
            assert "paragraph" in cleaned_content or "bold text" in cleaned_content


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
