# === 阶段一：构建依赖环境 ===
FROM ghcr.io/astral-sh/uv:python3.13-bookworm AS builder
WORKDIR /app

# 使用清华镜像源
ENV UV_INDEX="pypi-cn=https://mirrors.tuna.tsinghua.edu.cn/simple" \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

# 复制项目依赖定义
COPY pyproject.toml uv.lock /app/

# 安装依赖（不安装项目自身），依赖层可被缓存
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-install-project --no-dev

# 复制项目代码并安装项目
COPY . /app
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev

# === 阶段二：生产运行环境 ===
FROM python:3.13-slim-bookworm AS runtime
WORKDIR /app

# 将 builder 中的 .venv 搬进来
COPY --from=builder /app/.venv /app/.venv
COPY --from=builder /app /app

# 设置环境变量
ENV PATH="/app/.venv/bin:$PATH"

# 暴露 Leader 和 Employees 接口端口
EXPOSE 8231 8232

# 启动命令：同时启动 crawl4ai MCP 和 Leader MCP 服务
CMD ["bash", "-lc", "\
  crawl4ai serve-mcp --host 0.0.0.0 --port 8232 & \
  uv run leader_app:app --host 0.0.0.0 --port 8231 \
"]
