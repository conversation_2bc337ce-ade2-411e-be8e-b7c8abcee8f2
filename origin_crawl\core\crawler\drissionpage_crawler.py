"""
基于DrissionPage的爬虫实现 - 按照DrissionPage 4.1最佳实践优化
"""
import asyncio
import logging
from typing import Dict, List, Any, Tuple
from urllib.parse import urljoin
import time
import os
import psutil

# DrissionPage相关导入
from DrissionPage import Chromium
from DrissionPage import ChromiumOptions
from DrissionPage import ChromiumPage

from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler, CrawlResult
from specific_multi_scraper.utils.validators import is_valid_url, get_domain
from specific_multi_scraper.utils.content_cleaner import ContentCleaner
from specific_multi_scraper.utils.browser_manager import browser_manager

# 配置日志
logger = logging.getLogger(__name__)

class DrissionPageCrawler(BaseCrawler):
    """基于DrissionPage 4.1的爬虫实现"""

    def __init__(self, config: Dict[str, Any] = None, hooks=None):
        super().__init__(config, hooks)
        self.content_cleaner = ContentCleaner()
        self.browser = None  # Chromium对象
        self.tab = None      # Tab对象

    async def start(self):
        await super().start()  # 调用基类进程管理
        
        if self.browser is None:
            loop = asyncio.get_event_loop()
            
            # 在线程池中创建Chromium浏览器
            self.browser = await loop.run_in_executor(
                None, self._create_browser
            )
            
            # 获取标签页
            self.tab = self.browser.latest_tab
            
            # 注册浏览器进程
            try:
                # DrissionPage管理的Chrome进程
                import psutil
                current_process = psutil.Process(os.getpid())
                creation_time = time.time()
                
                # 延迟一下以确保Chrome进程已启动
                await asyncio.sleep(1)
                
                # 查找新的Chrome进程
                for proc in current_process.children(recursive=True):
                    try:
                        if ('chrome' in proc.name().lower() and 
                            proc.create_time() > creation_time - 5):
                            self.register_browser_process(proc.pid)
                            logger.debug(f"DrissionPage注册Chrome进程: {proc.pid}")
                    except:
                        pass
            except Exception as e:
                logger.error(f"注册DrissionPage浏览器进程失败: {e}")

    def _create_browser(self):
        """创建Chromium浏览器对象"""
        
        # 创建Chrome配置
        chrome_options = ChromiumOptions()
        
        # 设置无头模式 - 使用专用方法
        chrome_options.headless(True)
        
        # 使用正确的API设置参数
        chrome_options.set_argument('--no-sandbox')
        chrome_options.set_argument('--disable-gpu')
        chrome_options.set_argument('--disable-dev-shm-usage')
        chrome_options.set_argument('--disable-web-security')
        
        # 使用专用方法禁用图片
        chrome_options.no_imgs()
        
        # 其他参数
        chrome_options.set_argument('--disable-infobars')
        chrome_options.set_argument('--log-level', '3')
        
        # 设置窗口大小
        chrome_options.set_argument('--window-size', '1,1')
        
        # 创建Chromium对象
        try:
            browser = Chromium(chrome_options)
            logger.info("DrissionPage浏览器已启动 (无头模式)")
            return browser
        except Exception as e:
            logger.error(f"创建DrissionPage浏览器失败: {str(e)}")
            
            # 尝试使用最小配置
            try:
                minimal_options = ChromiumOptions()
                minimal_options.headless()  # 使用正确的方法设置无头模式
                browser = Chromium(minimal_options)
                logger.info("DrissionPage使用最小配置启动成功")
                return browser
            except Exception as e2:
                logger.error(f"最小配置也失败: {str(e2)}")
                raise

    async def close(self):
        """关闭资源"""
        if self.browser:
            # 释放进程
            for pid in list(self._browser_pids):
                browser_manager.unregister_process(pid)
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._quit_browser)
            self.browser = None
            self.tab = None
            
            # 强制垃圾回收
            import gc
            gc.collect()

    def _quit_browser(self):
        """安全关闭浏览器"""
        try:
            if self.browser:
                self.browser.quit()
        except Exception as e:
            logger.error(f"关闭浏览器异常: {str(e)}")

    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """爬取单个URL"""
        # 调用父类方法，触发钩子
        return await super().crawl(url, **kwargs)
    
    async def _crawl_impl(self, url: str, **kwargs) -> CrawlResult:
        """实际爬取实现"""
        if not is_valid_url(url):
            return CrawlResult.create_failure(
                url=url,
                error_type="InvalidURL",
                error_code="DRI001",
                description="无效的URL",
                crawler_used="drissionpage"
            )

        start_time = time.time()
        
        try:
            # 确保浏览器已初始化
            if self.browser is None:
                await self.start()

            # 获取当前标签页，如果不存在则获取latest_tab
            if not self.tab:
                self.tab = self.browser.latest_tab

            loop = asyncio.get_event_loop()
            max_execution_time = kwargs.get('max_execution_time', 60)
            
            # 访问网页并等待加载完成
            page_loaded = await asyncio.wait_for(
                loop.run_in_executor(None, lambda: self._navigate_to_url(url)),
                timeout=max_execution_time
            )

            if not page_loaded:
                return CrawlResult.create_failure(
                    url=url,
                    error_type="PageLoadError",
                    error_code="DRI002",
                    description="页面加载失败",
                    crawler_used="drissionpage"
                )

            # 获取页面内容
            html_content = await loop.run_in_executor(None, lambda: self.tab.html)
            
            # 提取链接
            links = []
            if kwargs.get('extract_links', True):
                raw_links = await loop.run_in_executor(None, self._extract_links)
                for link_data in raw_links:
                    href = link_data['href']
                    abs_link = urljoin(url, href)
                    links.append({
                        'href': abs_link,
                        'text': link_data['text']
                    })

            # 创建结果对象
            result = CrawlResult.create_success(
                url=url,
                content="",
                html=html_content if kwargs.get('save_html', True) else "",
                format="markdown",
                crawler_used="drissionpage"
            )

            # 添加链接和元数据
            result.links = links
            
            # 获取标题和更多元数据
            title = await loop.run_in_executor(None, lambda: self.tab.title)
            
            # 获取控制台日志
            console_logs = []
            if kwargs.get('collect_console', False):
                try:
                    console_logs = await loop.run_in_executor(None, lambda: list(self.tab.console))
                except:
                    pass
            
            result.metadata = {
                'title': title,
                'javascript_enabled': True,
                'dynamic_content': True,
                'crawl_time': time.time() - start_time,
                'console_logs': console_logs[:100] if console_logs else []  # 限制日志数量
            }

            # 处理内容
            if result.is_success() and self.content_cleaner:
                cleaner_result = self.content_cleaner.process_page_content(
                    html_content=html_content
                )
                result.content = cleaner_result.get("cleaned_content")
                
                if kwargs.get('save_html', True):
                    result.html = cleaner_result.get("cleaned_html")
            
            return result

        except asyncio.TimeoutError:
            return CrawlResult.create_failure(
                url=url,
                error_type="Timeout",
                error_code="DRI003",
                description="爬取超时",
                crawler_used="drissionpage"
            )
        except Exception as e:
            logger.error(f"爬取异常: {str(e)}")
            return CrawlResult.create_failure(
                url=url,
                error_type="CrawlError",
                error_code="DRI004",
                description=str(e),
                crawler_used="drissionpage"
            )

    def _navigate_to_url(self, url: str) -> bool:
        """访问网页并等待加载完成"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 访问网页
                self.tab.get(url)
                
                # 尝试不同的等待方法
                try:
                    self.tab.wait.load_or_presence()
                except AttributeError:
                    try:
                        self.tab.wait.document_loaded()
                    except AttributeError:
                        # 如果所有等待方法都失败，使用基本延时
                        import time
                        time.sleep(2)
                
                return True
                
            except Exception as e:
                retry_count += 1
                logger.warning(f"导航到 {url} 第 {retry_count} 次尝试失败: {str(e)}")
                if retry_count >= max_retries:
                    logger.error(f"导航到 {url} 失败，已重试 {max_retries} 次")
                    return False
                
                # 重试前等待
                import time
                time.sleep(1)
        
        return False

    def _extract_links(self) -> List[Dict[str, str]]:
        """提取页面上的所有链接"""
        links = []
        try:
            # 使用新的find方法查找所有链接
            link_elements = self.tab.eles('tag:a')

            for link in link_elements:
                try:
                    href = link.attr('href')
                    text = link.text

                    if href:  # 忽略没有href属性的链接
                        links.append({
                            'href': href,
                            'text': text
                        })
                except Exception:
                    # 忽略单个链接的提取错误
                    pass
        except Exception as e:
            logger.error(f"提取链接时出错: {str(e)}")

        return links

    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        # DrissionPage不能真正并发，但可以按顺序快速爬取
        results = {}

        for url in urls:
            kwargs['is_homepage'] = (url == kwargs.get(
                'base_url')) if kwargs.get('base_url') else False
            result = await self.crawl(url, **kwargs)
            results[url] = result

        return results

    async def crawl_site(self, base_url: str, **kwargs) -> Tuple[Dict[str, CrawlResult], List[str]]:
        """爬取整个网站 - 支持多层级递归爬取
        
        Returns:
            Tuple[Dict[str, CrawlResult], List[str]]: 成功结果和失败URL列表
        """
        max_urls = kwargs.get('max_urls', 100)
        include_external = kwargs.get('include_external', False)
        max_depth = kwargs.get('max_depth', 1)  # 默认爬取深度为1
        
        # 设置内存使用控制参数
        save_html = kwargs.get('save_html', False)  # 默认不保存HTML以节省内存

        # 初始化结果集和待爬取队列
        results = {}
        failed_urls = []  # 记录失败的URL
        crawled_urls = set()
        to_crawl_queue = [(base_url, 0)]  # (url, depth)
        
        try:
            # 爬取首页
            result = await self.crawl(base_url, is_homepage=True,
                                  extract_links=True,
                                  save_html=save_html,
                                  collect_console=True)  # 收集控制台日志
            results[base_url] = result
            crawled_urls.add(base_url)
            
            # 如果首页爬取失败，记录到失败列表
            if not result.is_success():
                failed_urls.append(base_url)

            # 提取首页链接
            if result.is_success() and result.links:
                links = result.get_link_hrefs()
                base_domain = get_domain(base_url)

                # 过滤链接
                if not include_external:
                    links = [link for link in links if get_domain(
                        link) == base_domain]
                
                # on_collect_sub_url钩子函数
                links = await self.process_collected_urls(base_url, [link for link in links if link not in crawled_urls])

                # 添加第一层链接到待爬取队列
                for link in links:
                    if link not in crawled_urls and len(to_crawl_queue) < max_urls:
                        to_crawl_queue.append((link, 1))

            # 广度优先爬取 - 由于DrissionPage限制，采用顺序爬取
            batch_count = 0
            while to_crawl_queue and len(crawled_urls) < max_urls:
                url, depth = to_crawl_queue.pop(0)

                # 爬取当前URL
                if url not in crawled_urls:
                    crawled_urls.add(url)
                    current_result = await self.crawl(
                        url,
                        base_url=base_url,
                        include_external=include_external,
                        extract_links=True,
                        save_html=save_html
                    )
                    results[url] = current_result
                    
                    # 如果爬取失败，记录到失败列表
                    if not current_result.is_success():
                        failed_urls.append(url)
                    
                    # 周期性垃圾回收
                    batch_count += 1
                    if batch_count % 5 == 0:  # 每爬取5个页面执行一次垃圾回收
                        import gc
                        gc.collect()

                    # 如果未达到最大深度，提取链接
                    if depth < max_depth and current_result.is_success() and current_result.links:
                        new_links = current_result.get_link_hrefs()

                        # 过滤链接
                        if not include_external:
                            new_links = [link for link in new_links if get_domain(
                                link) == base_domain]
                        
                        # on_collect_sub_url钩子函数
                        new_links = await self.process_collected_urls(url, new_links)

                        # 添加新链接到待爬取队列
                        for link in new_links:
                            if link not in crawled_urls and link not in [u for u, _ in to_crawl_queue] and len(crawled_urls) + len(to_crawl_queue) < max_urls:
                                to_crawl_queue.append((link, depth + 1))
            return results, failed_urls
        finally:
            # 确保资源释放
            if kwargs.get('close_after_crawl', True):
                await self.close()
    
    async def get_network_data(self, url: str, **kwargs):
        """获取网络请求数据"""
        try:
            if self.browser is None:
                await self.start()
                
            # 开始监听网络 - 使用新API
            self.tab.listen.start()
            
            # 访问页面
            self.tab.get(url)
            self.tab.wait.load_complete()
            
            # 等待网络静默
            await asyncio.sleep(kwargs.get('wait_time', 2))
            
            # 获取收集到的请求
            requests = self.tab.listen.requests
            responses = self.tab.listen.responses
            
            # 停止监听
            self.tab.listen.stop()
            
            return {
                'requests': requests,
                'responses': responses
            }
        except Exception as e:
            logger.error(f"获取网络数据失败: {str(e)}")
            return None
    
    async def get_console_logs(self, url: str, **kwargs):
        """获取控制台日志"""
        try:
            if self.browser is None:
                await self.start()
            
            # 开启控制台监听
            self.tab.listen.start()
            
            # 访问页面
            self.tab.get(url)
            self.tab.wait.load_complete()
            
            # 等待页面执行完毕
            await asyncio.sleep(kwargs.get('wait_time', 2))
            
            # 获取控制台日志 - 使用新API
            logs = self.tab.listen.console
            
            # 停止监听
            self.tab.listen.stop()
            
            return logs
        except Exception as e:
            logger.error(f"获取控制台日志失败: {str(e)}")
            return None
    
    async def take_screenshot(self, url: str, save_path: str = None, **kwargs):
        """截取页面截图"""
        try:
            if self.browser is None:
                await self.start()
            
            # 访问页面
            self.tab.get(url)
            self.tab.wait.load_complete()
            
            # 等待页面渲染完成
            await asyncio.sleep(kwargs.get('wait_time', 1))
            
            # 截图 - 使用新API
            if save_path:
                self.tab.get_screenshot(path=save_path)
                return {'success': True, 'path': save_path}
            else:
                # 如果未指定保存路径，返回截图数据
                screenshot_data = self.tab.get_screenshot(as_bytes=True)
                return {'success': True, 'data': screenshot_data}
        except Exception as e:
            logger.error(f"截取截图失败: {str(e)}")
            return {'success': False, 'error': str(e)}
 