#!/usr/bin/env python3
"""
启动Information Leader Agent的简化脚本
"""
import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse
    import uvicorn
    
    print("✅ FastAPI导入成功")
except ImportError as e:
    print(f"❌ FastAPI导入失败: {e}")
    sys.exit(1)

try:
    from common.models import ServerConfig
    print("✅ 公共模型导入成功")
except ImportError as e:
    print(f"❌ 公共模型导入失败: {e}")
    sys.exit(1)

# 创建一个简化的Leader测试应用
app = FastAPI(
    title="Information Leader Agent (Test)",
    description="A2A-compatible information collection leader agent",
    version="1.0.0"
)

@app.get("/")
async def root():
    return {"message": "Information Leader Agent is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "agent": "Information Leader"}

@app.get("/.well-known/agent.json")
async def get_agent_card():
    """返回简化的Leader Agent Card"""
    return {
        "name": "InformationLeaderAgent",
        "version": "1.0.0",
        "description": "Information collection group leader managing crawling and enrichment employees",
        "endpoint": "http://localhost:8231/a2a",
        "capabilities": [
            {
                "name": "coordinate_information_collection",
                "description": "Coordinate website crawling and information enrichment tasks",
                "inputs": [
                    {"type": "text", "name": "target_url"},
                    {"type": "text", "name": "company_name"}
                ],
                "outputs": [{"type": "object", "name": "enriched_information"}]
            }
        ],
        "skills": ["task-coordination", "employee-management", "information-integration"],
        "supports": {
            "streaming": True,
            "pushNotifications": True,
            "crossGroupCalls": True
        },
        "auth": ["none"],
        "protocolVersion": "1.0",
        "group": "information_collection",
        "employees": ["Crawl4AIEmployee"]
    }

@app.get("/employees")
async def get_employees():
    """返回Employee状态"""
    return {
        "employees": [
            {
                "name": "Crawl4AIEmployee",
                "endpoint": "http://localhost:8232/a2a",
                "capabilities": ["crawl_website"],
                "skills": ["web-crawling", "content-extraction"],
                "available": True
            }
        ]
    }

@app.get("/tasks")
async def get_active_tasks():
    """返回任务状态"""
    return {
        "active_tasks": 0,
        "task_ids": []
    }

@app.post("/a2a")
async def handle_a2a_request():
    """简化的A2A端点"""
    return {
        "jsonrpc": "2.0",
        "id": "test",
        "result": {
            "id": "test-task",
            "status": {"state": "completed"},
            "artifacts": [
                {
                    "name": "coordination_result",
                    "parts": [{"type": "text", "text": "Test coordination response"}]
                }
            ]
        }
    }

if __name__ == "__main__":
    print("🚀 启动Information Leader测试服务...")
    print("📍 服务地址: http://localhost:8231")
    print("📋 Agent Card: http://localhost:8231/.well-known/agent.json")
    print("🔗 A2A端点: http://localhost:8231/a2a")
    print("👥 Employees: http://localhost:8231/employees")
    print("📖 API文档: http://localhost:8231/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8231,
        log_level="info"
    )
