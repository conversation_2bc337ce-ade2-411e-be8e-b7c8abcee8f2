"""
A2A协议处理器
"""
import json
import logging
from typing import Dict, Any, Optional, Callable, Awaitable
from fastapi import HTT<PERSON>Exception

from .models import (
    A2ARequest, A2AResponse, A2AError, TaskParams, TaskResult, 
    TaskStatus, TaskState, Artifact, Part, PartType, AgentCard
)

logger = logging.getLogger(__name__)


class A2AProtocolHandler:
    """A2A协议处理器"""
    
    def __init__(self, agent_card: AgentCard):
        self.agent_card = agent_card
        self.task_handlers: Dict[str, Callable] = {}
        self.active_tasks: Dict[str, Any] = {}
        
    def register_handler(self, method: str, handler: Callable[[TaskParams], Awaitable[TaskResult]]):
        """注册任务处理器"""
        self.task_handlers[method] = handler
        logger.info(f"Registered handler for method: {method}")
    
    async def handle_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理A2A请求"""
        try:
            # 解析请求
            request = A2ARequest(**request_data)
            
            # 根据方法分发处理
            if request.method == "tasks/send":
                return await self._handle_task_send(request)
            elif request.method == "tasks/sendSubscribe":
                return await self._handle_task_subscribe(request)
            elif request.method == "tasks/cancel":
                return await self._handle_task_cancel(request)
            else:
                return self._create_error_response(
                    request.id, -32601, f"Method not found: {request.method}"
                )
                
        except Exception as e:
            logger.error(f"Error handling A2A request: {e}")
            return self._create_error_response(
                request_data.get("id", "unknown"), -32603, f"Internal error: {str(e)}"
            )
    
    async def _handle_task_send(self, request: A2ARequest) -> Dict[str, Any]:
        """处理tasks/send请求"""
        try:
            # 解析任务参数
            task_params = TaskParams(**request.params)
            
            # 提取任务内容
            task_content = self._extract_task_content(task_params.message)
            
            # 查找合适的处理器
            handler = self._find_handler(task_content)
            if not handler:
                return self._create_error_response(
                    request.id, -32600, "No suitable handler found for this task"
                )
            
            # 执行任务
            result = await handler(task_params)
            
            # 创建响应
            response = A2AResponse(
                id=request.id,
                result=result
            )
            
            return response.model_dump()
            
        except Exception as e:
            logger.error(f"Error in task_send: {e}")
            return self._create_error_response(
                request.id, -32603, f"Task execution failed: {str(e)}"
            )
    
    async def _handle_task_subscribe(self, request: A2ARequest) -> Dict[str, Any]:
        """处理tasks/sendSubscribe请求（流式）"""
        # 对于简单实现，我们先按照普通任务处理
        # 后续可以扩展为真正的流式处理
        return await self._handle_task_send(request)
    
    async def _handle_task_cancel(self, request: A2ARequest) -> Dict[str, Any]:
        """处理tasks/cancel请求"""
        try:
            task_id = request.params.get("id")
            if not task_id:
                return self._create_error_response(
                    request.id, -32602, "Missing task ID"
                )
            
            # 取消任务（简单实现）
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
                logger.info(f"Cancelled task: {task_id}")
            
            # 创建取消成功的响应
            result = TaskResult(
                id=task_id,
                status=TaskStatus(state=TaskState.CANCELLED, message="Task cancelled")
            )
            
            response = A2AResponse(id=request.id, result=result)
            return response.model_dump()
            
        except Exception as e:
            logger.error(f"Error in task_cancel: {e}")
            return self._create_error_response(
                request.id, -32603, f"Cancel failed: {str(e)}"
            )
    
    def _extract_task_content(self, message) -> Dict[str, Any]:
        """从消息中提取任务内容"""
        task_content = {}
        
        for part in message.parts:
            if part.type == PartType.TEXT and part.text:
                try:
                    # 尝试解析JSON
                    parsed = json.loads(part.text)
                    if isinstance(parsed, dict):
                        task_content.update(parsed)
                    else:
                        task_content["text"] = part.text
                except json.JSONDecodeError:
                    # 如果不是JSON，作为纯文本处理
                    task_content["text"] = part.text
            elif part.type == PartType.OBJECT and part.data:
                task_content.update(part.data)
        
        return task_content
    
    def _find_handler(self, task_content: Dict[str, Any]) -> Optional[Callable]:
        """根据任务内容查找合适的处理器"""
        # 简单的处理器匹配逻辑
        if "target_url" in task_content or "url" in task_content:
            return self.task_handlers.get("crawl_website")
        elif "base_url" in task_content:
            return self.task_handlers.get("crawl_site")
        
        # 默认处理器
        return self.task_handlers.get("default")
    
    def _create_error_response(self, request_id: Any, code: int, message: str) -> Dict[str, Any]:
        """创建错误响应"""
        error = A2AError(code=code, message=message)
        response = A2AResponse(id=request_id, error=error.model_dump())
        return response.model_dump()
    
    def get_agent_card(self) -> Dict[str, Any]:
        """获取Agent卡片"""
        return self.agent_card.model_dump()
    
    def create_success_result(self, task_id: str, artifacts: list, metadata: Optional[Dict] = None) -> TaskResult:
        """创建成功结果"""
        return TaskResult(
            id=task_id,
            status=TaskStatus(state=TaskState.COMPLETED, message="Task completed successfully"),
            artifacts=artifacts,
            metadata=metadata or {}
        )
    
    def create_failure_result(self, task_id: str, error_message: str, metadata: Optional[Dict] = None) -> TaskResult:
        """创建失败结果"""
        return TaskResult(
            id=task_id,
            status=TaskStatus(state=TaskState.FAILED, message=error_message),
            artifacts=[],
            metadata=metadata or {}
        )
    
    def create_text_artifact(self, name: str, content: str, description: Optional[str] = None) -> Artifact:
        """创建文本类型的Artifact"""
        return Artifact(
            name=name,
            description=description,
            parts=[Part(type=PartType.TEXT, text=content)]
        )
    
    def create_object_artifact(self, name: str, data: Dict[str, Any], description: Optional[str] = None) -> Artifact:
        """创建对象类型的Artifact"""
        return Artifact(
            name=name,
            description=description,
            parts=[Part(type=PartType.OBJECT, data=data)]
        )


def create_crawl4ai_agent_card() -> AgentCard:
    """创建Crawl4AI Employee的Agent Card"""
    from .models import AgentCapability, AgentSupports
    
    capabilities = [
        AgentCapability(
            name="crawl_website",
            description="Extract structured content from websites with intelligent parsing",
            inputs=[
                {"type": "text", "name": "url"},
                {"type": "boolean", "name": "include_links", "optional": True},
                {"type": "array", "name": "css_selectors", "optional": True}
            ],
            outputs=[
                {"type": "object", "name": "structured_content"},
                {"type": "array", "name": "extracted_links"},
                {"type": "object", "name": "metadata"}
            ]
        ),
        AgentCapability(
            name="crawl_site_batch",
            description="Batch crawl multiple URLs from a website",
            inputs=[
                {"type": "text", "name": "base_url"},
                {"type": "integer", "name": "max_pages", "optional": True},
                {"type": "integer", "name": "max_depth", "optional": True}
            ],
            outputs=[
                {"type": "object", "name": "site_content_map"}
            ]
        )
    ]
    
    supports = AgentSupports(
        streaming=True,
        pushNotifications=False,
        fallbackMechanisms=["playwright", "requests"]
    )
    
    return AgentCard(
        name="Crawl4AIEmployee",
        version="1.0.0",
        description="Advanced web crawler using Crawl4AI for structured content extraction",
        endpoint="http://crawl4ai-employee:8232/a2a",
        capabilities=capabilities,
        skills=["web-crawling", "content-extraction", "html-parsing", "javascript-rendering"],
        supports=supports,
        auth=["none"],
        protocolVersion="1.0",
        group="information_collection"
    )
