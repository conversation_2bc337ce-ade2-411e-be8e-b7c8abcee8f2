"""
Crawl4AI Employee Agent 测试
"""
import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

from common.models import CrawlOptions, CrawlResult, Crawl4AIConfig
from agents.employees.crawl4ai.crawler import SmartCrawlerCore
from agents.employees.crawl4ai.main import Crawl4<PERSON>IEmployee, ServerConfig


class TestSmartCrawlerCore:
    """智能爬虫核心测试"""
    
    @pytest.fixture
    def config(self):
        return Crawl4AIConfig(
            timeout=10,
            max_concurrent=2,
            enable_fallback=True,
            fallback_timeout=5
        )
    
    @pytest.fixture
    def crawler_core(self, config):
        return SmartCrawlerCore(config)
    
    @pytest.mark.asyncio
    async def test_crawl_url_success(self, crawler_core):
        """测试成功爬取URL"""
        options = CrawlOptions(url="https://example.com")
        
        # Mock Crawl4AI
        with patch.object(crawler_core, '_crawl_with_crawl4ai') as mock_crawl:
            mock_result = CrawlResult(
                url="https://example.com",
                success=True,
                content="Test content",
                html="<html>Test</html>",
                crawler_used="crawl4ai"
            )
            mock_crawl.return_value = mock_result
            
            result = await crawler_core.crawl_url(options)
            
            assert result.success is True
            assert result.url == "https://example.com"
            assert result.content == "Test content"
            assert result.crawler_used == "crawl4ai"
    
    @pytest.mark.asyncio
    async def test_crawl_url_fallback(self, crawler_core):
        """测试降级机制"""
        options = CrawlOptions(url="https://example.com")
        
        # Mock Crawl4AI失败，Playwright成功
        with patch.object(crawler_core, '_crawl_with_crawl4ai') as mock_crawl4ai, \
             patch.object(crawler_core, '_crawl_with_playwright') as mock_playwright:
            
            mock_crawl4ai.side_effect = Exception("Crawl4AI failed")
            mock_result = CrawlResult(
                url="https://example.com",
                success=True,
                content="Fallback content",
                html="<html>Fallback</html>",
                crawler_used="playwright"
            )
            mock_playwright.return_value = mock_result
            
            result = await crawler_core.crawl_url(options)
            
            assert result.success is True
            assert result.crawler_used == "playwright"
    
    @pytest.mark.asyncio
    async def test_crawl_url_all_failed(self, crawler_core):
        """测试所有方法都失败的情况"""
        options = CrawlOptions(url="https://example.com")
        
        # Mock所有方法都失败
        with patch.object(crawler_core, '_crawl_with_crawl4ai') as mock_crawl4ai, \
             patch.object(crawler_core, '_crawl_with_playwright') as mock_playwright, \
             patch.object(crawler_core, '_crawl_with_requests') as mock_requests:
            
            mock_crawl4ai.side_effect = Exception("Crawl4AI failed")
            mock_playwright.side_effect = Exception("Playwright failed")
            mock_requests.side_effect = Exception("Requests failed")
            
            result = await crawler_core.crawl_url(options)
            
            assert result.success is False
            assert "All methods failed" in result.error
    
    def test_filter_links(self, crawler_core):
        """测试链接过滤"""
        links = [
            {"href": "https://example.com/page1"},
            {"href": "https://other.com/page2"},
            {"href": "https://example.com/image.jpg"},
            {"href": "mailto:<EMAIL>"},
            {"href": "https://example.com/page3"}
        ]
        
        filtered = crawler_core._filter_links(
            links, "example.com", False, set()
        )
        
        # 应该过滤掉外部链接、图片和邮件链接
        assert len(filtered) == 2
        assert "https://example.com/page1" in filtered
        assert "https://example.com/page3" in filtered


class TestCrawl4AIEmployee:
    """Crawl4AI Employee Agent测试"""
    
    @pytest.fixture
    def server_config(self):
        return ServerConfig(host="127.0.0.1", port=8232)
    
    @pytest.fixture
    def crawl_config(self):
        return Crawl4AIConfig()
    
    @pytest.fixture
    def employee(self, crawl_config, server_config):
        return Crawl4AIEmployee(crawl_config, server_config)
    
    def test_agent_card_creation(self, employee):
        """测试Agent Card创建"""
        card = employee.agent_card
        
        assert card.name == "Crawl4AIEmployee"
        assert card.version == "1.0.0"
        assert len(card.capabilities) >= 1
        assert "web-crawling" in card.skills
        assert card.group == "information_collection"
    
    def test_fastapi_app_creation(self, employee):
        """测试FastAPI应用创建"""
        app = employee.app
        client = TestClient(app)
        
        # 测试健康检查端点
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
        
        # 测试Agent Card端点
        response = client.get("/.well-known/agent.json")
        assert response.status_code == 200
        card_data = response.json()
        assert card_data["name"] == "Crawl4AIEmployee"
    
    @pytest.mark.asyncio
    async def test_handle_crawl_website(self, employee):
        """测试网站爬取任务处理"""
        from common.models import TaskParams, Message, Part, PartType, MessageRole
        
        # 创建任务参数
        task_params = TaskParams(
            id="test-task-1",
            message=Message(
                role=MessageRole.USER,
                parts=[Part(
                    type=PartType.TEXT,
                    text=json.dumps({
                        "url": "https://example.com",
                        "include_links": True
                    })
                )]
            )
        )
        
        # Mock爬虫核心
        with patch.object(employee.crawler_core, 'crawl_url') as mock_crawl:
            mock_result = CrawlResult(
                url="https://example.com",
                success=True,
                content="Test content",
                html="<html>Test</html>",
                metadata={"title": "Test Page"},
                crawler_used="crawl4ai"
            )
            mock_crawl.return_value = mock_result
            
            result = await employee._handle_crawl_website(task_params)
            
            assert result.status.state.value == "completed"
            assert len(result.artifacts) >= 1
            assert result.artifacts[0].name == "structured_content"


class TestA2AIntegration:
    """A2A协议集成测试"""
    
    @pytest.fixture
    def employee_app(self):
        """创建测试用的Employee应用"""
        config = Crawl4AIConfig()
        server_config = ServerConfig(host="127.0.0.1", port=8232)
        employee = Crawl4AIEmployee(config, server_config)
        return TestClient(employee.app)
    
    def test_a2a_request_format(self, employee_app):
        """测试A2A请求格式"""
        request_data = {
            "jsonrpc": "2.0",
            "id": "test-request-1",
            "method": "tasks/send",
            "params": {
                "id": "test-task-1",
                "message": {
                    "role": "user",
                    "parts": [
                        {
                            "type": "text",
                            "text": json.dumps({
                                "url": "https://example.com"
                            })
                        }
                    ]
                },
                "metadata": {}
            }
        }
        
        # Mock爬虫功能
        with patch('agents.employees.crawl4ai.main.SmartCrawlerCore') as mock_core_class:
            mock_core = AsyncMock()
            mock_core_class.return_value = mock_core
            mock_core.crawl_url.return_value = CrawlResult(
                url="https://example.com",
                success=True,
                content="Test content",
                crawler_used="crawl4ai"
            )
            
            response = employee_app.post("/a2a", json=request_data)
            
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["jsonrpc"] == "2.0"
            assert response_data["id"] == "test-request-1"
            assert "result" in response_data


@pytest.mark.integration
class TestEndToEndIntegration:
    """端到端集成测试"""
    
    @pytest.mark.asyncio
    async def test_real_website_crawl(self):
        """测试真实网站爬取（需要网络连接）"""
        config = Crawl4AIConfig(timeout=30)
        crawler = SmartCrawlerCore(config)
        
        try:
            await crawler.initialize()
            
            options = CrawlOptions(
                url="https://httpbin.org/html",
                include_links=False,
                timeout=10
            )
            
            result = await crawler.crawl_url(options)
            
            assert result.success is True
            assert result.content is not None
            assert len(result.content) > 0
            
        finally:
            await crawler.close()
    
    def test_docker_compose_config(self):
        """测试Docker Compose配置"""
        import yaml
        
        with open("docker-compose.yml", "r") as f:
            compose_config = yaml.safe_load(f)
        
        # 验证服务配置
        assert "info-leader" in compose_config["services"]
        assert "crawl4ai-employee" in compose_config["services"]
        
        # 验证端口配置
        leader_ports = compose_config["services"]["info-leader"]["ports"]
        assert any("8231" in port for port in leader_ports)
        
        crawl4ai_ports = compose_config["services"]["crawl4ai-employee"]["ports"]
        assert any("8232" in port for port in crawl4ai_ports)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
