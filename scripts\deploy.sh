#!/bin/bash

# Information Collection Agent 部署脚本
# 用于快速部署和管理A2A信息收集智能体组

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，从 .env.example 复制..."
        cp .env.example .env
        log_info "请编辑 .env 文件配置必要的环境变量"
    fi
    
    log_success "依赖检查完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建Leader Agent
    log_info "构建Information Leader Agent..."
    docker build -f agents/leader/Dockerfile -t info-leader:latest .
    
    # 构建Crawl4AI Employee
    log_info "构建Crawl4AI Employee Agent..."
    docker build -f agents/employees/crawl4ai/Dockerfile -t crawl4ai-employee:latest .
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 创建网络
    docker network create information-collection-network 2>/dev/null || true
    
    # 启动核心服务
    docker-compose up -d info-leader crawl4ai-employee
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services
    
    log_success "服务启动完成"
}

# 启动完整服务（包括可选组件）
start_full_services() {
    log_info "启动完整服务（包括可选组件）..."
    
    # 启动所有服务
    docker-compose --profile exa --profile redis up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    check_services
    
    log_success "完整服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查Leader Agent
    if curl -f http://localhost:8231/health &>/dev/null; then
        log_success "Information Leader Agent 运行正常"
    else
        log_error "Information Leader Agent 无法访问"
    fi
    
    # 检查Crawl4AI Employee
    if curl -f http://localhost:8232/health &>/dev/null; then
        log_success "Crawl4AI Employee Agent 运行正常"
    else
        log_error "Crawl4AI Employee Agent 无法访问"
    fi
    
    # 检查Agent Cards
    log_info "检查Agent Cards..."
    
    if curl -f http://localhost:8231/.well-known/agent.json &>/dev/null; then
        log_success "Leader Agent Card 可访问"
    else
        log_warning "Leader Agent Card 无法访问"
    fi
    
    if curl -f http://localhost:8232/.well-known/agent.json &>/dev/null; then
        log_success "Crawl4AI Employee Agent Card 可访问"
    else
        log_warning "Crawl4AI Employee Agent Card 无法访问"
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services
}

# 查看日志
view_logs() {
    local service=${1:-""}
    
    if [ -z "$service" ]; then
        log_info "查看所有服务日志..."
        docker-compose logs -f
    else
        log_info "查看 $service 服务日志..."
        docker-compose logs -f "$service"
    fi
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 安装测试依赖
    pip install pytest pytest-asyncio pytest-cov
    
    # 运行单元测试
    log_info "运行单元测试..."
    python -m pytest tests/ -v --cov=agents --cov=common
    
    # 运行集成测试（如果服务正在运行）
    if curl -f http://localhost:8231/health &>/dev/null; then
        log_info "运行集成测试..."
        python -m pytest tests/ -v -m integration
    else
        log_warning "服务未运行，跳过集成测试"
    fi
    
    log_success "测试完成"
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 停止并删除容器
    docker-compose down --volumes --remove-orphans
    
    # 删除镜像（可选）
    read -p "是否删除构建的镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rmi info-leader:latest crawl4ai-employee:latest 2>/dev/null || true
        log_success "镜像已删除"
    fi
    
    # 删除网络
    docker network rm information-collection-network 2>/dev/null || true
    
    log_success "清理完成"
}

# 显示状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo
    log_info "网络状态:"
    docker network ls | grep information-collection || echo "网络未创建"
    
    echo
    log_info "端口使用情况:"
    netstat -tlnp 2>/dev/null | grep -E ":(8231|8232|8233)" || echo "相关端口未被占用"
}

# 显示帮助信息
show_help() {
    echo "Information Collection Agent 部署脚本"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  check       检查系统依赖"
    echo "  build       构建Docker镜像"
    echo "  start       启动核心服务"
    echo "  start-full  启动完整服务（包括可选组件）"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      显示服务状态"
    echo "  logs [服务] 查看日志"
    echo "  test        运行测试"
    echo "  cleanup     清理所有资源"
    echo "  help        显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 check           # 检查依赖"
    echo "  $0 build           # 构建镜像"
    echo "  $0 start           # 启动服务"
    echo "  $0 logs info-leader # 查看Leader日志"
    echo "  $0 test            # 运行测试"
}

# 主函数
main() {
    case "${1:-help}" in
        check)
            check_dependencies
            ;;
        build)
            check_dependencies
            build_images
            ;;
        start)
            check_dependencies
            build_images
            start_services
            ;;
        start-full)
            check_dependencies
            build_images
            start_full_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            view_logs "$2"
            ;;
        test)
            run_tests
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
