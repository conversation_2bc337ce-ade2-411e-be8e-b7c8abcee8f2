"""
Information Leader Agent 任务协调器
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
import aiohttp
import json

from common.models import (
    TaskParams, TaskResult, TaskStatus, TaskState, 
    Artifact, Part, PartType, A2ARequest, A2AResponse
)

logger = logging.getLogger(__name__)


class EmployeeInfo:
    """Employee信息"""
    def __init__(self, name: str, endpoint: str, capabilities: List[str], skills: List[str]):
        self.name = name
        self.endpoint = endpoint
        self.capabilities = capabilities
        self.skills = skills
        self.available = True
        self.last_used = None


class TaskCoordinator:
    """任务协调器"""
    
    def __init__(self):
        self.employees: Dict[str, EmployeeInfo] = {}
        self.active_tasks: Dict[str, Any] = {}
        self._initialize_employees()
    
    def _initialize_employees(self):
        """初始化Employee信息"""
        # Crawl4AI Employee
        self.employees["crawl4ai"] = EmployeeInfo(
            name="Crawl4AIEmployee",
            endpoint="http://crawl4ai-employee:8232/a2a",
            capabilities=["crawl_website", "crawl_site_batch"],
            skills=["web-crawling", "content-extraction", "html-parsing"]
        )
        
        # Exa Employee (如果需要的话)
        self.employees["exa"] = EmployeeInfo(
            name="ExaSearchEmployee", 
            endpoint="http://exa-employee:8233/a2a",
            capabilities=["search_and_enrich", "company_research"],
            skills=["ai-search", "information-enrichment", "company-research"]
        )
        
        logger.info(f"Initialized {len(self.employees)} employees")
    
    async def coordinate_information_collection(self, task_params: TaskParams) -> TaskResult:
        """协调信息收集任务"""
        try:
            # 提取任务内容
            task_content = self._extract_task_content(task_params.message)
            
            # 分析任务类型
            task_type = self._analyze_task_type(task_content)
            
            if task_type == "single_url_crawl":
                return await self._handle_single_url_crawl(task_params, task_content)
            elif task_type == "site_crawl":
                return await self._handle_site_crawl(task_params, task_content)
            elif task_type == "information_enrichment":
                return await self._handle_information_enrichment(task_params, task_content)
            elif task_type == "comprehensive_analysis":
                return await self._handle_comprehensive_analysis(task_params, task_content)
            else:
                return self._create_failure_result(
                    task_params.id, f"Unknown task type: {task_type}"
                )
                
        except Exception as e:
            logger.error(f"Error in coordinate_information_collection: {e}")
            return self._create_failure_result(
                task_params.id, f"Coordination failed: {str(e)}"
            )
    
    def _extract_task_content(self, message) -> Dict[str, Any]:
        """从消息中提取任务内容"""
        task_content = {}
        
        for part in message.parts:
            if part.type == PartType.TEXT and part.text:
                try:
                    parsed = json.loads(part.text)
                    if isinstance(parsed, dict):
                        task_content.update(parsed)
                    else:
                        task_content["text"] = part.text
                except json.JSONDecodeError:
                    task_content["text"] = part.text
            elif part.type == PartType.OBJECT and part.data:
                task_content.update(part.data)
        
        return task_content
    
    def _analyze_task_type(self, task_content: Dict[str, Any]) -> str:
        """分析任务类型"""
        if "target_url" in task_content or "url" in task_content:
            if "company_name" in task_content or "priority_keywords" in task_content:
                return "comprehensive_analysis"
            else:
                return "single_url_crawl"
        elif "base_url" in task_content:
            return "site_crawl"
        elif "query" in task_content or "search_term" in task_content:
            return "information_enrichment"
        else:
            # 默认尝试单URL爬取
            return "single_url_crawl"
    
    async def _handle_single_url_crawl(self, task_params: TaskParams, task_content: Dict[str, Any]) -> TaskResult:
        """处理单URL爬取任务"""
        # 选择Crawl4AI Employee
        employee = self.employees.get("crawl4ai")
        if not employee:
            return self._create_failure_result(task_params.id, "Crawl4AI employee not available")
        
        # 构建请求
        crawl_request = {
            "target_url": task_content.get("target_url") or task_content.get("url"),
            "include_links": task_content.get("include_links", True),
            "extract_metadata": task_content.get("extract_metadata", True)
        }
        
        # 调用Employee
        result = await self._call_employee(employee, "crawl_website", crawl_request, task_params.id)
        
        if result:
            return self._create_success_result(
                task_params.id, 
                result.get("artifacts", []),
                {"employee_used": "crawl4ai", "task_type": "single_url_crawl"}
            )
        else:
            return self._create_failure_result(task_params.id, "Failed to crawl URL")
    
    async def _handle_site_crawl(self, task_params: TaskParams, task_content: Dict[str, Any]) -> TaskResult:
        """处理网站爬取任务"""
        employee = self.employees.get("crawl4ai")
        if not employee:
            return self._create_failure_result(task_params.id, "Crawl4AI employee not available")
        
        # 构建网站爬取请求
        site_request = {
            "base_url": task_content.get("base_url"),
            "max_pages": task_content.get("max_pages", 50),
            "max_depth": task_content.get("max_depth", 2),
            "include_external": task_content.get("include_external", False)
        }
        
        # 调用Employee
        result = await self._call_employee(employee, "crawl_site", site_request, task_params.id)
        
        if result:
            return self._create_success_result(
                task_params.id,
                result.get("artifacts", []),
                {"employee_used": "crawl4ai", "task_type": "site_crawl"}
            )
        else:
            return self._create_failure_result(task_params.id, "Failed to crawl site")
    
    async def _handle_information_enrichment(self, task_params: TaskParams, task_content: Dict[str, Any]) -> TaskResult:
        """处理信息补全任务"""
        # 这里可以调用Exa Employee进行信息补全
        # 目前先返回一个简单的响应
        artifacts = [
            Artifact(
                name="enrichment_result",
                description="Information enrichment placeholder",
                parts=[Part(
                    type=PartType.TEXT,
                    text="Information enrichment feature will be implemented with Exa Employee"
                )]
            )
        ]
        
        return self._create_success_result(
            task_params.id,
            artifacts,
            {"employee_used": "placeholder", "task_type": "information_enrichment"}
        )
    
    async def _handle_comprehensive_analysis(self, task_params: TaskParams, task_content: Dict[str, Any]) -> TaskResult:
        """处理综合分析任务（爬取+补全）"""
        try:
            # 第一步：爬取网站内容
            crawl_result = await self._handle_single_url_crawl(task_params, task_content)
            
            if crawl_result.status.state != TaskState.COMPLETED:
                return crawl_result
            
            # 第二步：信息补全（目前简化处理）
            company_name = task_content.get("company_name", "Unknown Company")
            
            # 合并结果
            combined_artifacts = crawl_result.artifacts.copy()
            
            # 添加分析摘要
            analysis_summary = {
                "company_name": company_name,
                "analysis_type": "comprehensive",
                "crawl_successful": True,
                "enrichment_status": "placeholder",
                "priority_keywords": task_content.get("priority_keywords", [])
            }
            
            combined_artifacts.append(
                Artifact(
                    name="analysis_summary",
                    description="Comprehensive analysis summary",
                    parts=[Part(type=PartType.OBJECT, data=analysis_summary)]
                )
            )
            
            return self._create_success_result(
                task_params.id,
                combined_artifacts,
                {"task_type": "comprehensive_analysis", "steps_completed": ["crawl", "analysis"]}
            )
            
        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {e}")
            return self._create_failure_result(task_params.id, f"Comprehensive analysis failed: {str(e)}")
    
    async def _call_employee(self, employee: EmployeeInfo, method: str, params: Dict[str, Any], task_id: str) -> Optional[Dict[str, Any]]:
        """调用Employee"""
        try:
            # 构建A2A请求
            request_data = {
                "jsonrpc": "2.0",
                "id": f"leader-{task_id}",
                "method": "tasks/send",
                "params": {
                    "id": task_id,
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "type": "text",
                                "text": json.dumps(params)
                            }
                        ]
                    },
                    "metadata": {"method": method}
                }
            }
            
            # 发送HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    employee.endpoint,
                    json=request_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "result" in result:
                            logger.info(f"Successfully called {employee.name} for {method}")
                            return result["result"]
                        else:
                            logger.error(f"Employee {employee.name} returned error: {result.get('error')}")
                            return None
                    else:
                        logger.error(f"HTTP error calling {employee.name}: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error calling employee {employee.name}: {e}")
            return None
    
    def _create_success_result(self, task_id: str, artifacts: List[Artifact], metadata: Optional[Dict] = None) -> TaskResult:
        """创建成功结果"""
        return TaskResult(
            id=task_id,
            status=TaskStatus(state=TaskState.COMPLETED, message="Task completed successfully"),
            artifacts=artifacts,
            metadata=metadata or {}
        )
    
    def _create_failure_result(self, task_id: str, error_message: str, metadata: Optional[Dict] = None) -> TaskResult:
        """创建失败结果"""
        return TaskResult(
            id=task_id,
            status=TaskStatus(state=TaskState.FAILED, message=error_message),
            artifacts=[],
            metadata=metadata or {}
        )
