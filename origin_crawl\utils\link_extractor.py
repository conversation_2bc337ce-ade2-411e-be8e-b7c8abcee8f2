"""
链接提取工具模块，用于从爬取内容中提取URL链接
"""
import re
import logging
from urllib.parse import urljoin, urlparse, unquote
from typing import List, Set, Dict, Tuple
import tldextract

# 配置日志
logger = logging.getLogger(__name__)

def extract_links_from_markdown(content: str, base_url: str = None) -> List[str]:
    """从Markdown内容中提取所有链接
    
    Args:
        content: Markdown格式的内容
        base_url: 基础URL，用于解析相对链接
        
    Returns:
        提取的链接列表
    """
    # 提取Markdown链接格式 [文本](链接)
    md_links = re.findall(r'\[.*?\]\((.*?)\)', content)
    
    # 提取HTML链接格式 <a href="链接">
    html_links = re.findall(r'<a\s+(?:[^>]*?\s+)?href="([^"]*)"', content)
    
    # 合并所有链接
    all_links = md_links + html_links
    
    # 如果提供了基础URL，解析相对链接
    if base_url:
        all_links = [urljoin(base_url, link) for link in all_links]
    
    # 过滤掉非HTTP(S)链接，如javascript:void(0)等
    valid_links = []
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.ico']
    for link in all_links:
        # 解码URL以便正确判断
        decoded_link = unquote(link)
        
        # 跳过JavaScript链接
        if link.startswith('javascript:') or 'javascript:void' in link:
            continue
            
        # 确保是http或https链接
        if not link.startswith('http'):
            continue
            
        # 跳过图片链接 - 检查扩展名
        is_image = False
        for ext in image_extensions:
            if decoded_link.lower().endswith(ext):
                is_image = True
                break
        
        # 跳过图片目录
        if '/content/dam/' in decoded_link and any(img_word in decoded_link.lower() for img_word in ['image', 'img', 'picture', 'photo', 'flag', 'icon', 'banner', 'logo']):
            is_image = True
        
        if is_image:
            continue
        
        valid_links.append(link)
    
    # 移除URL中的片段标识符和查询参数，保留基本URL
    clean_links = []
    seen_paths = set()  # 用于去重
    
    for link in valid_links:
        parsed = urlparse(link)
        # 移除查询参数和片段标识符
        clean_link = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        
        # 标准化路径：确保尾部有斜杠或没有，保持一致性
        if not parsed.path:
            clean_link = f"{clean_link}/"
        elif not clean_link.endswith('/') and '.' not in parsed.path.split('/')[-1]:
            # 如果路径没有扩展名且不以/结尾，添加/
            clean_link = f"{clean_link}/"
            
        # 路径去重
        path_key = f"{parsed.netloc}{parsed.path}"
        if path_key not in seen_paths:
            seen_paths.add(path_key)
            clean_links.append(clean_link)
    
    return clean_links

def filter_links_by_domain(links: List[str], base_domain: str, include_subdomains: bool = True) -> Tuple[List[str], List[str]]:
    """按域名过滤链接
    
    Args:
        links: 链接列表
        base_domain: 基础域名
        include_subdomains: 是否包含子域名
        
    Returns:
        (内部链接列表, 外部链接列表)
    """
    internal_links = []
    external_links = []
    
    # 提取基础域名的信息
    base_info = tldextract.extract(base_domain)
    base_root_domain = f"{base_info.domain}.{base_info.suffix}"
    
    # 排除链接黑名单（正则模式）
    blacklist_patterns = [
        r'\.zip$',         # ZIP文件
        r'\.rar$',         # RAR文件
        r'\.docx?$',       # Word文件
        r'\.xlsx?$',       # Excel文件
        r'\.pptx?$',       # PowerPoint文件
        r'/logout',        # 登出链接
        r'/login',         # 登录链接（可能导致会话问题）
        r'/search\?',      # 搜索结果页
        r'/content/dam/',  # 内容资源库（通常是图片或媒体文件）
    ]
    
    for link in links:
        # 跳过黑名单中的链接
        skip = False
        for pattern in blacklist_patterns:
            if re.search(pattern, link, re.IGNORECASE):
                skip = True
                break
        
        if skip:
            continue
            
        # 提取链接域名信息
        link_info = tldextract.extract(link)
        link_domain = f"{link_info.domain}.{link_info.suffix}"
        
        # 判断是否为内部链接
        if link_domain == base_root_domain:
            # 如果要包含子域名，或者是完全匹配的域名
            if include_subdomains or (link_info.subdomain == base_info.subdomain):
                internal_links.append(link)
            else:
                external_links.append(link)
        else:
            external_links.append(link)
    
    return internal_links, external_links

def prioritize_links(links: List[str], priority_keywords: List[str] = None) -> List[str]:
    """对链接进行优先级排序
    
    Args:
        links: 链接列表
        priority_keywords: 优先级关键词列表
        
    Returns:
        排序后的链接列表
    """
    if not priority_keywords:
        priority_keywords = [
            'about', 'product', 'service', 'business', 'career', 
            'contact', 'investor', 'news', 'overview'
        ]
    
    # 对链接进行评分
    link_scores = []
    for link in links:
        score = 0
        lowered = link.lower()
        
        # 根据关键词匹配度评分
        for i, keyword in enumerate(priority_keywords):
            if keyword.lower() in lowered:
                # 关键词在列表前面的权重更高
                score += (len(priority_keywords) - i) 
        
        # URL长度短的优先级更高（通常是主要页面）
        score -= len(link) * 0.01
        
        link_scores.append((link, score))
    
    # 按评分排序（降序）
    sorted_links = [link for link, score in sorted(link_scores, key=lambda x: x[1], reverse=True)]
    return sorted_links

def categorize_links(links: List[str]) -> Dict[str, List[str]]:
    """按域名对链接进行分类
    
    Args:
        links: 链接列表
        
    Returns:
        按域名分类的链接字典
    """
    categorized = {}
    
    for link in links:
        domain = tldextract.extract(link).registered_domain
        if not domain:
            domain = urlparse(link).netloc
        
        if domain not in categorized:
            categorized[domain] = []
        
        categorized[domain].append(link)
    
    return categorized

def safe_filename(url: str) -> str:
    """将URL转换为安全的文件名
    
    Args:
        url: URL字符串
        
    Returns:
        安全的文件名
    """
    # 移除URL协议和特殊字符
    filename = url.replace("://", "_")
    filename = re.sub(r'[\\/*?:"<>|]', "_", filename)
    filename = re.sub(r'__+', "_", filename)  # 替换连续的下划线
    
    # 处理长度
    if len(filename) > 200:  # 避免文件名过长
        parsed = urlparse(url)
        domain = parsed.netloc
        path_hash = hash(parsed.path) % 10000  # 使用路径的简单哈希
        filename = f"{domain}_{path_hash}"
    
    return filename 