"""
简化的浏览器实例管理器 - 更可靠地处理Chrome进程
"""
import logging
import psutil
import os
import signal
import time
import asyncio
import atexit
from typing import Dict, List, Set

logger = logging.getLogger(__name__)

class BrowserManager:
    """简化的浏览器管理器"""
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = BrowserManager()
        return cls._instance
    
    def __init__(self):
        """初始化管理器"""
        self.chrome_pids = set()  # 所有已知的Chrome进程PID
        self.exit_handler_registered = False
        self.register_exit_handler()
    
    def register_exit_handler(self):
        """注册退出处理程序"""
        if not self.exit_handler_registered:
            atexit.register(self.cleanup_all)
            self.exit_handler_registered = True
    
    def register_process(self, pid: int) -> bool:
        """注册Chrome进程"""
        if pid and pid > 0:
            self.chrome_pids.add(pid)
            logger.debug(f"注册Chrome进程: {pid}")
            return True
        return False
    
    def unregister_process(self, pid: int) -> bool:
        """注销Chrome进程"""
        if pid in self.chrome_pids:
            self.chrome_pids.remove(pid)
            logger.debug(f"注销Chrome进程: {pid}")
            return True
        return False
    
    async def cleanup_idle(self):
        """清理空闲超过5分钟的Chrome进程"""
        # 在这个简化版中，我们不尝试确定哪些进程是空闲的
        # 而是依赖于各个爬虫模块自己关闭浏览器
        pass
    
    def cleanup_all(self):
        """清理所有Chrome进程"""
        chrome_count = len(self.chrome_pids)
        if chrome_count > 0:
            logger.info(f"清理所有浏览器进程，共{chrome_count}个")
            for pid in list(self.chrome_pids):
                try:
                    self._terminate_process(pid)
                    self.chrome_pids.remove(pid)
                except Exception as e:
                    logger.debug(f"终止进程{pid}失败: {e}")
        
        # 查找和清理可能遗漏的Chrome进程
        self._cleanup_chrome_processes()
    
    def _terminate_process(self, pid: int):
        """终止进程"""
        try:
            process = psutil.Process(pid)
            if process.is_running():
                process.terminate()
                
                # 给进程一些时间来优雅地关闭
                try:
                    process.wait(timeout=2)
                except psutil.TimeoutExpired:
                    # 如果进程没有在2秒内终止，强制杀死
                    process.kill()
        except psutil.NoSuchProcess:
            # 进程已经不存在
            pass
        except Exception as e:
            logger.error(f"终止进程 {pid} 时出错: {e}")
    
    def _cleanup_chrome_processes(self):
        """查找并清理与当前程序相关的Chrome进程"""
        try:
            # 获取当前进程
            current_process = psutil.Process(os.getpid())
            
            # 查找所有子进程
            for child in current_process.children(recursive=True):
                try:
                    if child.is_running() and "chrome" in child.name().lower():
                        logger.info(f"终止残留Chrome进程: {child.pid}")
                        child.terminate()
                        try:
                            child.wait(timeout=2)
                        except psutil.TimeoutExpired:
                            child.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    pass
                except Exception as e:
                    logger.error(f"清理Chrome进程时出错: {e}")
        except Exception as e:
            logger.error(f"查找Chrome进程时出错: {e}")

# 创建单例实例
browser_manager = BrowserManager.get_instance() 