"""
基于Crawl4AI的官网爬取系统

这是一个基于Crawl4AI构建的官网爬取系统，支持异步并发爬取多个网址，
并具有降级机制确保稳定性。
"""
from specific_multi_scraper.version import __version__
from specific_multi_scraper.core.main_crawler import MainCrawler as Crawler
from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler
from specific_multi_scraper.core.result import CrawlResult
from specific_multi_scraper.utils.content_cleaner import ContentCleaner
from specific_multi_scraper.core import CrawlerConfig
from specific_multi_scraper.core import CrawlerHooks


__all__ = ["Crawler", "BaseCrawler", "CrawlResult", "ContentCleaner",
           "CrawlerConfig", "CrawlerHooks"]
