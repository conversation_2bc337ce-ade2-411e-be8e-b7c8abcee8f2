import asyncio
import logging
import time
from typing import Dict, List, Any,Set, Tuple,Callable
import os
# 导入自定义模块
from .crawler.base_crawler import CrawlResult
# 修改Pydantic导入 - 使用dataclasses替代BaseSettings
from dataclasses import dataclass
from ..utils.validators import get_domain
import functools
from collections import defaultdict
# 配置日志
logger = logging.getLogger(__name__)
# 添加重试装饰器
def retry(max_attempts=3, retry_delay=1):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except asyncio.TimeoutError as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        # 最后一次尝试失败
                        logger.warning(f"操作超时，已重试{attempt}次: {str(e)}")
                    else:
                        logger.debug(f"操作超时，重试中({attempt+1}/{max_attempts}): {str(e)}")
                        await asyncio.sleep(retry_delay)
                except Exception as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        # 最后一次尝试失败
                        logger.warning(f"操作失败，已重试{attempt}次: {str(e)}")
                    else:
                        logger.debug(f"操作失败，重试中({attempt+1}/{max_attempts}): {str(e)}")
                        await asyncio.sleep(retry_delay)
            
            # 所有尝试都失败了
            if isinstance(last_exception, asyncio.TimeoutError):
                return CrawlResult.create_failure(
                    url=kwargs.get('url', "unknown"),
                    error_type="Timeout",
                    error_code="CR002",
                    description=f"操作超时(重试{max_attempts}次)",
                    crawler_used="main"
                )
            else:
                return CrawlResult.create_failure(
                    url=kwargs.get('url', "unknown"),
                    error_type="Error",
                    error_code="CR003",
                    description=str(last_exception) if last_exception else "未知错误",
                    crawler_used="main"
                )
                
        return wrapper
    return decorator

@dataclass
class CrawlerConfig:
    """爬虫配置类 - 使用dataclass替代BaseSettings"""
    max_domain_concurrent: int = 10
    request_timeout: int = 60  # 秒
    include_external: bool = True
    max_urls: int = 1000
    max_depth: int = 3
    max_concurrent: int = 5
    handle_failed_urls: bool = True
    
    def to_dict(self):
        return self.__dict__

class LinkManager:
    """链接管理器"""
    def __init__(self, base_domain: str, include_external: bool = False):
        self.base_domain = base_domain
        self.include_external = include_external
        self.discovered_links = set()
        self.crawled_links = set()
        self.to_crawl = set()
        self.to_crawl_queue = []
        self.filtered_links = defaultdict(list)
        self.page_discovered_links = {}
        self.external_links = set()  # 单独存储外部链接
        
    def add_discovered_links(self, url: str, links: List[Any]) -> Tuple[List[str], Dict[str, List[str]]]:
        """处理新发现的链接
        
        Args:
            url: 页面URL
            links: 链接列表，可以是字符串列表或包含href属性的字典列表
            
        Returns:
            links_to_add: 要添加到爬取队列的链接列表
            filtered: 按类别过滤的链接
        """
        links_to_add = []
        filtered = defaultdict(list)
        
        # 提取href - 确保是字符串列表
        hrefs = []
        for link in links:
            if isinstance(link, dict) and link.get("href"):
                hrefs.append(link.get("href"))
            elif isinstance(link, str):
                hrefs.append(link)
        
        # 记录发现的链接
        self.page_discovered_links[url] = hrefs
        self.discovered_links.update(hrefs)
        
        for link in hrefs:
            if self._should_filter(link):
                category = self._get_filter_category(link)
                filtered[category].append(link)
            elif self._should_add(link):
                links_to_add.append(link)
                
        return links_to_add, filtered
    
    def _should_filter(self, link: str) -> bool:
        """判断链接是否应该被过滤"""
        if link.lower().endswith('.pdf'):
            return True
        doc_extensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar']
        return any(link.lower().endswith(ext) for ext in doc_extensions)
    
    def _get_filter_category(self, link: str) -> str:
        """获取过滤链接的类别"""
        if link.lower().endswith('.pdf'):
            return "pdf"
        return "documents"
    
    def _should_add(self, link: str) -> bool:
        """判断链接是否应该被添加到爬取队列"""
        if link in self.crawled_links or link in self.to_crawl:
            return False
        
        # 检查是否为外部链接
        is_external = get_domain(link) != self.base_domain
        
        if is_external:
            # 如果是外部链接，记录下来
            self.external_links.add(link)
            # 如果不包含外部链接，则返回False
            if not self.include_external:
                return False
        
        return True
    
    def mark_as_crawled(self, url: str):
        """标记URL为已爬取"""
        self.crawled_links.add(url)
        self.to_crawl.discard(url)
        
    def add_to_crawl(self, url: str, depth: int = 0):
        """添加URL到待爬取队列"""
        if url not in self.to_crawl and url not in self.crawled_links:
            self.to_crawl.add(url)
            self.to_crawl_queue.append((url, depth))
            
    def get_next_batch(self, batch_size: int, priority_keywords: List[str] = None) -> List[Tuple[str, int]]:
        """获取下一批要爬取的URL"""
        if not self.to_crawl_queue:
            return []
            
        if priority_keywords:
            # 按优先级排序
            urls = [url for url, _ in self.to_crawl_queue]
            depths = {url: depth for url, depth in self.to_crawl_queue}
            sorted_urls = self._prioritize_urls(urls, priority_keywords)
            self.to_crawl_queue = [(url, depths[url]) for url in sorted_urls]
            
        batch = self.to_crawl_queue[:batch_size]
        self.to_crawl_queue = self.to_crawl_queue[batch_size:]
        return batch
        
    def _prioritize_urls(self, urls: List[str], keywords: List[str]) -> List[str]:
        """按关键词优先级排序URL"""
        def url_priority(url: str) -> int:
            return sum(1 for kw in keywords if kw.lower() in url.lower())
        return sorted(urls, key=url_priority, reverse=True)
        
    def has_urls_to_crawl(self) -> bool:
        """是否还有URL待爬取"""
        return bool(self.to_crawl_queue) or bool(self.to_crawl)
        
    def get_stats(self) -> Dict[str, int]:
        """获取链接统计信息"""
        return {
            "discovered": len(self.discovered_links),
            "crawled": len(self.crawled_links),
            "to_crawl": len(self.to_crawl),
            "filtered": sum(len(links) for links in self.filtered_links.values()),
            "external_links": len(self.external_links)  # 添加外部链接统计
        }

class CrawlerState:
    """爬虫状态管理器"""
    def __init__(self):
        self.active_crawls = set()
        self.domain_semaphores = {}
        self.domain_last_crawl = {}
        self.stats = {
            "successful_crawls": 0,
            "failed_crawls": 0,
            "fallback_crawls": 0,
            "start_time": time.time()
        }
        
    async def acquire_domain_lock(self, domain: str, max_domain_concurrent: int = 3):
        """获取域名锁"""
        if domain not in self.domain_semaphores:
            self.domain_semaphores[domain] = asyncio.Semaphore(max_domain_concurrent)
        return self.domain_semaphores[domain]
        
    def update_domain_last_crawl(self, domain: str):
        """更新域名最后爬取时间"""
        self.domain_last_crawl[domain] = time.time()
        
    def should_wait_domain(self, domain: str) -> float:
        """检查是否需要等待域名冷却"""
        if domain in self.domain_last_crawl:
            elapsed = time.time() - self.domain_last_crawl[domain]
            if elapsed < 0.5:
                return 0.5 - elapsed
        return 0
        
    def add_active_crawl(self, url: str):
        """添加活跃爬取任务"""
        self.active_crawls.add(url)
        
    def remove_active_crawl(self, url: str):
        """移除活跃爬取任务"""
        self.active_crawls.discard(url)
        
    def update_stats(self, success: bool = True, fallback: bool = False):
        """更新统计信息"""
        if success:
            self.stats["successful_crawls"] += 1
        else:
            self.stats["failed_crawls"] += 1
        if fallback:
            self.stats["fallback_crawls"] += 1
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats["elapsed_time"] = time.time() - stats["start_time"]
        return stats

class CrawlerHooks:
    """爬虫钩子"""
    def __init__(self):
        # 不再需要hooks参数，每个方法由子类实现或使用默认实现
        pass
    
    async def execute_hook(self, hook_name: str, *args, **kwargs):
        """执行钩子函数，自动处理同步和异步钩子
        
        Args:
            hook_name: One of the hook method names
            *args, **kwargs: 传递给钩子函数的参数
            
        Returns:
            钩子函数的返回值
        """
        method = getattr(self, hook_name, None)
        if method and callable(method):
            try:
                # 检查方法是否是异步的
                if asyncio.iscoroutinefunction(method):
                    # 异步方法，使用await调用
                    return await method(*args, **kwargs)
                else:
                    # 同步方法，直接调用
                    return method(*args, **kwargs)
            except Exception as e:
                logger.warning(f"执行钩子方法 {hook_name} 时出错: {str(e)}")
                return self._get_default_result(hook_name, args)
        
        return self._get_default_result(hook_name, args)
    
    def _get_default_result(self, hook_name: str, args):
        """获取钩子的默认返回值"""
        # 对于on_collect_sub_url钩子，默认返回原始sub_url
        if hook_name == "on_collect_sub_url" and len(args) > 1:
            return args[1]  # 返回sub_url参数
        # 对于parse_pdf_file钩子，默认返回原始pdf_file
        elif hook_name == "parse_pdf_file" and len(args) > 1:
            return args[1]  # 返回pdf_file参数
        return None
    
    def on_crawl_start(self, url: str):
        """爬取开始时"""    
        pass
    
    def on_crawl_success(self, url: str, result: CrawlResult):
        """爬取成功时"""
        pass
    
    def on_crawl_failure(self, url: str, error: Exception):
        """爬取失败时"""
        pass
    
    def on_collect_sub_url(self, url: str, sub_url: List[str]) -> List[str]:
        """收集到子链接时"""
        return sub_url
    
    def on_collect_sub_url_failure(self, url: str, sub_url: List[str], error: Exception):
        """收集子链接失败时"""
        return sub_url
    
    def parse_pdf_file(self, url: str, pdf_file: bytes):
        """解析PDF文件"""
        return pdf_file
    
    def on_recover_failed_urls(self, failed_urls: List[str]):
        """恢复失败URL时"""
        pass

