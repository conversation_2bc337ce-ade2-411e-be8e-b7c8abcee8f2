"""
基于Selenium的爬虫实现
"""
import asyncio
import logging
from typing import Dict, List, Any, Tuple
from urllib.parse import urljoin
import time
import re

# Selenium相关导入
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler, CrawlResult
from specific_multi_scraper.utils.validators import is_valid_url, get_domain
from specific_multi_scraper.utils.content_cleaner import ContentCleaner
from specific_multi_scraper.utils.browser_manager import browser_manager

# 配置日志
logger = logging.getLogger(__name__)


class SeleniumCrawler(BaseCrawler):
    """基于Selenium的爬虫实现"""

    def __init__(self, config: Dict[str, Any] = None, hooks=None):
        super().__init__(config, hooks)
        self.content_cleaner = ContentCleaner()
        self._driver = None

    async def start(self):
        """启动Selenium爬虫"""
        logger.info("开始启动Selenium爬虫...")
        start_time = time.time()
        
        try:
            await super().start()  # 调用基类进程管理
            logger.debug("基类进程管理启动完成")
            
            if self._driver is None:
                loop = asyncio.get_event_loop()
                
                try:
                    # 在线程池中创建WebDriver
                    logger.debug("开始创建WebDriver...")
                    self._driver = await loop.run_in_executor(
                        None, self._create_driver
                    )
                    logger.debug("WebDriver创建完成")
                    
                    # 验证驱动是否创建成功
                    if not self._driver:
                        raise RuntimeError("WebDriver创建失败")
                        
                    # 注册浏览器进程
                    try:
                        # 获取Chrome进程ID并注册
                        service = self._driver.service
                        if hasattr(service, 'process') and service.process:
                            pid = service.process.pid
                            self.register_browser_process(pid)
                            logger.debug(f"Selenium注册Chrome进程: {pid}")
                            
                            # 额外检查子进程
                            import psutil
                            try:
                                process = psutil.Process(pid)
                                for child in process.children():
                                    if 'chrome' in child.name().lower():
                                        self.register_browser_process(child.pid)
                                        logger.debug(f"Selenium注册Chrome子进程: {child.pid}")
                            except Exception as e:
                                logger.debug(f"检查子进程时出错: {str(e)}")
                    except Exception as e:
                        logger.error(f"注册浏览器进程失败: {str(e)}")
                        
                except Exception as e:
                    logger.error(f"启动Selenium WebDriver失败: {str(e)}")
                    if self._driver:
                        try:
                            self._driver.quit()
                        except:
                            pass
                    raise
                    
            elapsed_time = time.time() - start_time
            logger.info(f"Selenium爬虫启动完成，耗时: {elapsed_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"Selenium爬虫启动失败: {str(e)}")
            raise

    def _create_driver(self):
        """在新线程中创建WebDriver"""
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--headless=new')  # 新版Chrome的headless参数
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        
        # 移除单进程模式，这可能导致proxy resolver错误
        # options.add_argument('--single-process')  # 使用单进程
        
        # 使用多进程模式代替
        options.add_argument('--disable-features=site-per-process')
        
        # 添加内存控制选项
        options.add_argument('--js-flags=--expose-gc')
        
        # 明确设置headless
        options.headless = True
        
        service = Service()
        driver = webdriver.Chrome(options=options, service=service)
        driver.set_page_load_timeout(30)
        return driver

    def _create_driver_fallback(self):
        """创建WebDriver的备用方法"""
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        try:
            logger.info("尝试使用备用方法创建WebDriver")
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            # 尝试直接创建Chrome WebDriver，不使用service对象
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            logger.error(f"备用方法1创建WebDriver失败: {str(e)}")
            
            # 尝试第二种备用方法 - 不使用任何选项
            try:
                logger.info("尝试使用备用方法2创建WebDriver (无选项)")
                driver = webdriver.Chrome()
                driver.set_page_load_timeout(30)
                return driver
            except Exception as e:
                logger.error(f"备用方法2创建WebDriver也失败: {str(e)}")
                raise

    async def close(self):
        if self._driver:
            # 注销浏览器进程
            try:
                service = self._driver.service
                if hasattr(service, 'process') and service.process:
                    pid = service.process.pid
                    # 修改为使用browser_manager直接注销
                    browser_manager.unregister_process(pid)
                    
                    # 清理子进程
                    import psutil
                    try:
                        process = psutil.Process(pid)
                        for child in process.children():
                            if 'chrome' in child.name().lower():
                                browser_manager.unregister_process(child.pid)
                    except:
                        pass
            except:
                pass
            
            # 关闭浏览器
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._quit_driver)
            self._driver = None
            
            # 强制垃圾回收
            import gc
            gc.collect()

    def _quit_driver(self):
        """安全关闭WebDriver"""
        try:
            # 关闭所有窗口
            if hasattr(self._driver, 'window_handles'):
                for _ in self._driver.window_handles:
                    self._driver.close()
        except:
            pass
        
        try:
            # 退出驱动
            self._driver.quit()
        except:
            pass

    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """爬取单个URL"""
        # 调用父类方法，触发钩子
        return await super().crawl(url, **kwargs)
        
    async def _crawl_impl(self, url: str, **kwargs) -> CrawlResult:
        """实际的爬取实现"""
        if not is_valid_url(url):
            return CrawlResult.create_failure(
                url=url,
                error_type="InvalidURL",
                error_code="SEL001",
                description="无效的URL",
                crawler_used="selenium"
            )

        start_time = time.time()
        try:
            # 如果爬虫没有初始化，先初始化
            if self._driver is None:
                await self.start()

            # 设置更严格的超时
            timeout = self.config.get('timeout', 30)
            
            # 使用asyncio.wait_for进行超时控制
            result = await asyncio.wait_for(
                self._do_crawl(url, **kwargs),
                timeout=timeout
            )
            
            return result
        except asyncio.TimeoutError:
            # 超时处理
            logger.warning(f"爬取{url}超时")
            # 尝试重置浏览器状态
            try:
                if self._driver:
                    await self.close()
                    # 重新初始化
                    await self.start()
            except:
                pass
            
            return CrawlResult.create_failure(
                url=url,
                error_type="Timeout",
                error_code="SE002",
                description="Selenium爬取超时",
                crawler_used="selenium"
            )
        except Exception as e:
            logger.error(f"爬取异常: {str(e)}")
            return CrawlResult.create_failure(
                url=url,
                error_type="CrawlError",
                error_code="SEL004",
                description=str(e),
                crawler_used="selenium"
            )

    def _navigate_to_url(self, url: str, timeout: int) -> bool:
        """导航到指定URL

        Args:
            url: 要导航的URL
            timeout: 超时时间(秒)

        Returns:
            是否成功导航
        """
        try:
            self._driver.get(url)
            return True
        except (TimeoutException, WebDriverException) as e:
            logger.error(f"导航到 {url} 失败: {str(e)}")
            return False

    def _extract_links(self) -> List[Dict[str, str]]:
        """提取页面上的所有链接

        Returns:
            链接列表
        """
        links = []
        try:
            # 查找所有链接元素
            link_elements = self._driver.find_elements(By.TAG_NAME, "a")

            for link in link_elements:
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()

                    if href:  # 忽略没有href属性的链接
                        links.append({
                            'href': href,
                            'text': text
                        })
                except Exception:
                    # 忽略单个链接的提取错误
                    pass
        except Exception as e:
            logger.error(f"提取链接时出错: {str(e)}")

        return links

    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        # Selenium不能真正并发，但可以按顺序快速爬取
        results = {}

        for url in urls:
            kwargs['is_homepage'] = (url == kwargs.get(
                'base_url')) if kwargs.get('base_url') else False
            result = await self.crawl(url, **kwargs)
            results[url] = result

        return results

    async def crawl_site(self, base_url: str, **kwargs) -> Tuple[Dict[str, CrawlResult], List[str]]:
        """爬取整个网站 - 支持多层级递归爬取
        
        Returns:
            Tuple[Dict[str, CrawlResult], List[str]]: 成功结果和失败URL列表
        """
        max_urls = kwargs.get('max_urls', 100)
        include_external = kwargs.get('include_external', False)
        max_depth = kwargs.get('max_depth', 1)  # 默认爬取深度为1
        save_html = kwargs.get('save_html', True)  # 默认保存HTML

        # 初始化结果集和待爬取队列
        results = {}
        failed_urls = []  # 记录失败的URL
        crawled_urls = set()
        to_crawl_queue = [(base_url, 0)]  # (url, depth)
        
        try:
            # 爬取首页
            result = await self.crawl(base_url, is_homepage=True,
                                    extract_links=True,
                                    save_html=save_html)
            results[base_url] = result
            crawled_urls.add(base_url)

            # 如果首页爬取失败，记录失败
            if not result.is_success():
                failed_urls.append(base_url)

            # 提取首页链接
            if result.is_success() and result.links:
                links = result.get_link_hrefs()
                base_domain = get_domain(base_url)

                # 过滤链接
                if not include_external:
                    links = [link for link in links if get_domain(
                        link) == base_domain]

                # on_collect_sub_url钩子函数
                links = await self.process_collected_urls(base_url, [link for link in links if link not in crawled_urls])
                
                # 添加第一层链接到待爬取队列
                for link in links:
                    if link not in crawled_urls and len(to_crawl_queue) < max_urls:
                        to_crawl_queue.append((link, 1))

            # 广度优先爬取 - 由于Selenium限制，采用顺序爬取
            batch_count = 0
            while to_crawl_queue and len(crawled_urls) < max_urls:
                # 获取当前批次URL，按域名分组防止单一域名过多请求
                current_batch = []
                current_depths = {}
                domain_counts = {}
                
                # 最多处理10个URL作为一个批次
                batch_size = min(10, len(to_crawl_queue))
                for _ in range(batch_size):
                    if not to_crawl_queue:
                        break
                        
                    url, depth = to_crawl_queue.pop(0)
                    
                    if url not in crawled_urls:
                        domain = get_domain(url)
                        # 确保每个域名在当前批次中最多有3个URL
                        if domain_counts.get(domain, 0) < 3:
                            current_batch.append(url)
                            current_depths[url] = depth
                            domain_counts[domain] = domain_counts.get(domain, 0) + 1
                            crawled_urls.add(url)
                
                if not current_batch:
                    break
                
                # 顺序爬取当前批次
                for url in current_batch:
                    depth = current_depths[url]
                    current_result = await self.crawl(
                        url,
                        base_url=base_url,
                        include_external=include_external,
                        extract_links=True,
                        save_html=save_html
                    )
                    results[url] = current_result
                    
                    # 如果爬取失败，记录到失败列表
                    if not current_result.is_success():
                        failed_urls.append(url)
                    
                    # 周期性垃圾回收
                    batch_count += 1
                    if batch_count % 5 == 0:  # 每爬取5个页面执行一次垃圾回收
                        import gc
                        gc.collect()

                    # 如果未达到最大深度，提取链接
                    if depth < max_depth and current_result.is_success() and current_result.links:
                        new_links = current_result.get_link_hrefs()

                        # 过滤链接
                        if not include_external:
                            new_links = [link for link in new_links if get_domain(
                                link) == base_domain]

                        # on_collect_sub_url钩子函数
                        new_links = await self.process_collected_urls(url, new_links)
                        
                        # 添加新链接到待爬取队列
                        for link in new_links:
                            if link not in crawled_urls and link not in [u for u, _ in to_crawl_queue] and len(crawled_urls) + len(to_crawl_queue) < max_urls:
                                to_crawl_queue.append((link, depth + 1))

            return results, failed_urls
        finally:
            # 确保资源释放
            if kwargs.get('close_after_crawl', True):
                await self.close()

    async def _do_crawl(self, url: str, **kwargs) -> CrawlResult:
        """执行爬取操作的内部方法"""
        loop = asyncio.get_event_loop()
        
        # 在线程池中执行导航
        timeout = kwargs.get('timeout', 30)
        page_loaded = await loop.run_in_executor(
            None, lambda: self._navigate_to_url(url, timeout)
        )
        
        if not page_loaded:
            return CrawlResult.create_failure(
                url=url,
                error_type="PageLoadError",
                error_code="SEL002",
                description="页面加载失败",
                crawler_used="selenium"
            )
        
        # 等待页面渲染完成
        load_time = kwargs.get('load_time', 2)
        await asyncio.sleep(load_time)
        
        # 获取页面HTML
        html_content = await loop.run_in_executor(
            None, lambda: self._driver.page_source
        )
        
        # 提取链接
        links = []
        if kwargs.get('extract_links', True):
            raw_links = await loop.run_in_executor(
                None, self._extract_links
            )
            
            for link_data in raw_links:
                href = link_data['href']
                links.append({
                    'href': href,
                    'text': link_data['text']
                })
        
        # 获取页面标题
        title = await loop.run_in_executor(
            None, lambda: self._driver.title
        )
        
        # 创建结果对象
        result = CrawlResult.create_success(
            url=url,
            content="",
            html=html_content if kwargs.get('save_html', True) else "",
            format="html",
            crawler_used="selenium"
        )
        
        # 添加链接和元数据
        result.links = links
        result.metadata = {
            'title': title,
            'javascript_enabled': True,
            'dynamic_content': True,
            'crawl_time': time.time() - kwargs.get('start_time', time.time())
        }
        
        # 处理内容清理
        if result.is_success() and self.content_cleaner:
            cleaner_result = self.content_cleaner.process_page_content(
                html_content=html_content
            )
            result.content = cleaner_result.get("cleaned_content", "")
            
            if kwargs.get('save_html', True):
                result.html = cleaner_result.get("cleaned_html", html_content)
        
        return result
