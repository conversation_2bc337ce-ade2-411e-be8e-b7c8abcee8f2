"""
格式化工具模块，用于处理爬取内容的格式化
"""
import re
from typing import Dict, List, Optional, Any
import json
from bs4 import BeautifulSoup

def format_markdown(content: str) -> str:
    """格式化Markdown内容
    
    1. 移除脚本代码块
    2. 保留图片链接
    3. 保持表格结构
    4. 脱敏敏感信息
    
    Args:
        content: Markdown格式的内容
        
    Returns:
        格式化后的Markdown内容
    """
    # 移除JavaScript代码块
    content = re.sub(r'```javascript.*?```', '', content, flags=re.DOTALL)
    content = re.sub(r'```js.*?```', '', content, flags=re.DOTALL)
    
    # 脱敏敏感信息 (电话、邮箱、身份证)
    content = mask_sensitive_info(content)
    
    return content

def mask_sensitive_info(text: str) -> str:
    """脱敏敏感信息
    
    处理以下敏感信息：
    1. 电话号码
    2. 邮箱地址
    3. 身份证号码
    
    Args:
        text: 需要脱敏的文本
        
    Returns:
        脱敏后的文本
    """
    # 脱敏手机号
    text = re.sub(r'1[3-9]\d{9}', '1xx****xxxx', text)
    
    # 脱敏邮箱
    def mask_email(match):
        email = match.group(0)
        parts = email.split('@')
        if len(parts) != 2:
            return email
        
        username = parts[0]
        domain = parts[1]
        
        if len(username) <= 2:
            masked_username = username[0] + '*' * (len(username) - 1)
        else:
            masked_username = username[0] + '*' * (len(username) - 2) + username[-1]
            
        return f"{masked_username}@{domain}"
    
    text = re.sub(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', mask_email, text)
    
    # 脱敏身份证号
    text = re.sub(r'\d{17}[\dXx]', '**************xxxx', text)
    
    return text

def remove_ads(html_content: str) -> str:
    """移除广告和导航元素
    
    Args:
        html_content: HTML内容
        
    Returns:
        移除广告后的HTML内容
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 移除常见广告元素
    ad_selectors = [
        'div.ad', 'div.advertisement', 'div.banner', 
        'div.ads', 'iframe', '.ad-container', 
        '[id*="ad-"]', '[class*="ad-"]', '[id*="banner"]',
        'aside', 'nav'
    ]
    
    for selector in ad_selectors:
        for element in soup.select(selector):
            element.decompose()
    
    return str(soup)

def chunk_content(content: str, chunk_size: int = 1000) -> List[str]:
    """将内容分块处理
    
    当内容超过一定长度时，将其分块处理
    
    Args:
        content: 需要分块的内容
        chunk_size: 每块的大小（字符数）
        
    Returns:
        分块后的内容列表
    """
    # 如果内容长度小于块大小，直接返回
    if len(content) <= chunk_size:
        return [content]
    
    # 以段落为单位分块
    paragraphs = re.split(r'\n\s*\n', content)
    chunks = []
    current_chunk = ""
    
    for para in paragraphs:
        # 如果当前段落加上当前块的长度小于块大小，则添加到当前块
        if len(current_chunk) + len(para) + 2 <= chunk_size:
            if current_chunk:
                current_chunk += "\n\n" + para
            else:
                current_chunk = para
        # 否则创建新块
        else:
            if current_chunk:
                chunks.append(current_chunk)
            # 如果单个段落大于块大小，则需要分割该段落
            if len(para) > chunk_size:
                # 尝试按句子分割
                sentences = re.split(r'(?<=[.!?])\s+', para)
                current_chunk = ""
                for sentence in sentences:
                    if len(current_chunk) + len(sentence) + 1 <= chunk_size:
                        if current_chunk:
                            current_chunk += " " + sentence
                        else:
                            current_chunk = sentence
                    else:
                        chunks.append(current_chunk)
                        current_chunk = sentence
            else:
                current_chunk = para
    
    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)
    
    return chunks 