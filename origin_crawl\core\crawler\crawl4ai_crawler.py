"""
Crawl4AI爬虫实现
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple

from crawl4ai import Async<PERSON>eb<PERSON>raw<PERSON>, CrawlerRunConfig
from crawl4ai.content_filter_strategy import Pruning<PERSON>ontentFilter
from crawl4ai.markdown_generation_strategy import Default<PERSON><PERSON>downGenerator

from specific_multi_scraper.core.crawler.base_crawler import BaseCrawler, CrawlResult
from specific_multi_scraper.utils.validators import is_valid_url, get_domain
from specific_multi_scraper.utils.content_cleaner import ContentCleaner

# 配置日志
logger = logging.getLogger(__name__)

class Crawl4AICrawler(BaseCrawler):
    """基于Crawl4AI的爬虫实现"""

    def __init__(self, config: Dict[str, Any] = None, hooks=None):
        super().__init__(config, hooks)
        self.content_cleaner = ContentCleaner()
        
    async def get_crawler(self):
        """获取或创建爬虫实例"""
        if self._crawler is None:
            await self.start()
        return self._crawler

    async def start(self):
        if self._crawler is None:
            self._crawler = AsyncWebCrawler()
            await self._crawler.start()
            
            # 注册浏览器进程
            try:
                if hasattr(self._crawler, '_browser') and self._crawler._browser:
                    try:
                        pid = self._crawler._browser.process.pid
                        self.register_browser_process(pid)
                    except Exception as e:
                        logger.warning(f"注册Crawl4AI浏览器进程失败: {e}")
            except Exception as e:
                logger.warning(f"获取Crawl4AI浏览器进程失败: {e}")

    async def close(self):
        if self._crawler is not None:
            try:
                # 关闭爬虫
                await self._crawler.close()
            except Exception as e:
                logger.error(f"关闭Crawl4AI爬虫时出错: {str(e)}")
            finally:
                # 清理浏览器进程记录
                await super().close()
                self._crawler = None

    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """爬取单个URL"""
        # 调用父类方法，触发钩子
        return await super().crawl(url, **kwargs)
        
    async def _crawl_impl(self, url: str, **kwargs) -> CrawlResult:
        """实际的爬取实现"""
        if not is_valid_url(url):
            return CrawlResult.create_failure(
                url=url,
                error_type="InvalidURL",
                error_code="CR001",
                description="无效的URL",
                crawler_used="crawl4ai"
            )
        
        # 处理PDF文件
        if url.lower().endswith(('.pdf')):
            return await self.process_pdf_file(url)
        
        # 处理不支持的文件类型
        if url.lower().endswith(('.jpg', '.png', '.gif', '.mp4', '.avi', '.mov', '.mp3', '.wav', '.m4a', '.ogg', '.webm','docx')):
            logger.info(f"find unsupported file type: {url},type:{url.split('.')[-1]}, skip")
            return CrawlResult.create_success(
                url=url,
                content='crawler system is not suitable for processing this file type',
                crawler_used="crawl4ai",
            )
            
        try:
            crawler = await self.get_crawler()
            config = self._build_config(**kwargs)
            
            # 设置更严格的超时控制
            timeout = self.config.get('timeout', 30)
            result = await asyncio.wait_for(
                crawler.arun(url=url, config=config),
                timeout=timeout
            )
            
            # 转换结果
            crawl_result = CrawlResult.from_crawl4ai_result(result)
            
            if crawl_result.is_success() and self.content_cleaner:
                # 处理内容
                crawl_result.raw_html = crawl_result.html
                cleaner_result = self.content_cleaner.process_page_content(
                    html_content=crawl_result.html
                )
                crawl_result.content = cleaner_result.get("cleaned_content")
                crawl_result.html = cleaner_result.get("cleaned_html")
            return crawl_result
                
        except asyncio.TimeoutError:
            # 超时时，确保资源释放
            try:
                if hasattr(crawler, '_page') and crawler._page:
                    await crawler._page.close()
            except:
                pass
            
            return CrawlResult.create_failure(
                url=url,
                error_type="Timeout",
                error_code="CR002",
                description="爬取超时",
                crawler_used="crawl4ai"
            )
        except Exception as e:
            # 异常时，也确保资源释放
            try:
                if hasattr(crawler, '_page') and crawler._page:
                    await crawler._page.close()
            except:
                pass
            
            return CrawlResult.create_failure(
                url=url,
                error_type="CrawlError",
                error_code="CR003",
                description=str(e),
                crawler_used="crawl4ai"
            )

    async def crawl_many(self, urls: List[str], **kwargs) -> Dict[str, CrawlResult]:
        """并发爬取多个URL"""
        max_concurrent = kwargs.get('max_concurrent', 
                                  self.config.get('max_concurrent_tasks', 10))
        base_url = kwargs.get('base_url')
            
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str) -> Tuple[str, CrawlResult]:
            async with semaphore:
                kwargs['is_homepage'] = (url == base_url) if base_url else False
                result = await self.crawl(url, **kwargs)
                return url, result
                
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = []
        
        for task in asyncio.as_completed(tasks):
            try:
                result = await task
                results.append(result)
            except Exception as e:
                logger.error(f"爬取任务异常: {str(e)}")
                
        return {url: result for url, result in results}
        
    async def crawl_site(self, base_url: str, **kwargs) -> Dict[str, CrawlResult]:
        """爬取整个网站 - 支持多层级递归爬取"""
        max_urls = kwargs.get('max_urls', 100)
        include_external = kwargs.get('include_external', False)
        max_concurrent = kwargs.get('max_concurrent', 
                                  self.config.get('max_concurrent_tasks', 10))
        max_depth = kwargs.get('max_depth', 1)  # 默认爬取深度为1
        
        # 初始化结果集和待爬取队列
        results = {}
        crawled_urls = set()
        to_crawl_queue = [(base_url, 0)]  # (url, depth)
        
        # 记录爬取失败的URL
        failed_urls = []
        
        # 爬取首页
        result = await self.crawl(base_url, is_homepage=True, 
                                include_external=include_external)
        
        # 如果首页爬取失败，整个爬取过程失败
        if not result.is_success():
            logger.error(f"首页 {base_url} 爬取失败: {result.error.description if result.error else '未知错误'}")
            results[base_url] = result
            return results
            
        # 首页爬取成功，正常处理
        results[base_url] = result
        crawled_urls.add(base_url)
        
        # 提取首页链接
        if result.links:
            links = [link.get('href') for link in result.links if link.get('href')]
            base_domain = get_domain(base_url)
            
            # 过滤链接
            if not include_external:
                links = [link for link in links if get_domain(link) == base_domain]
                
            # 应用子链接收集钩子 - 改为异步调用
            links = await self.process_collected_urls(base_url, [link for link in links if link not in crawled_urls])

                
            # 添加第一层链接到待爬取队列
            for link in links:
                if link not in crawled_urls and len(to_crawl_queue) < max_urls:
                    
                    to_crawl_queue.append((link, 1))
        
        # 广度优先爬取
        while to_crawl_queue and len(crawled_urls) < max_urls:
            # 获取当前批次要爬取的URL
            current_batch = []
            current_depths = {}
            
            while to_crawl_queue and len(current_batch) < max_concurrent:
                url, depth = to_crawl_queue.pop(0)
                if url not in crawled_urls:
                    current_batch.append(url)
                    current_depths[url] = depth
                    crawled_urls.add(url)
            
            if not current_batch:
                break
                
            # 并发爬取当前批次
            batch_results = await self.crawl_many(
                current_batch, 
                max_concurrent=max_concurrent,
                base_url=base_url,
                include_external=include_external
            )
            
            # 更新结果集，同时处理失败的URL
            for url, result in batch_results.items():
                if result.is_success():
                    results[url] = result
                else:
                    # 记录失败的URL，但不添加到结果集
                    logger.warning(f"URL {url} 爬取失败: {result.error.description if result.error else '未知错误'}")
                    failed_urls.append(url)
                    # 从已爬取集合中移除，以便可能的重试
                    crawled_urls.remove(url)
            
            # 处理当前批次中的链接，准备下一层爬取
            for url, result in batch_results.items():
                # 跳过失败的URL，不处理其链接
                if not result.is_success():
                    continue
                    
                current_depth = current_depths.get(url, 0)
                
                # 如果达到最大深度，不再提取链接
                if current_depth >= max_depth:
                    continue
                    
                # 提取新的链接
                if result.links:
                    new_links = [link.get('href') for link in result.links if link.get('href')]
                    
                    # 过滤链接
                    if not include_external:
                        new_links = [link for link in new_links if get_domain(link) == base_domain]
                    
                    # 应用子链接收集钩子 - 改为异步调用
                    new_links = await self.process_collected_urls(url, new_links)
                    
                    # 添加新链接到待爬取队列
                    for link in new_links:
                        if link not in crawled_urls and len(crawled_urls) + len(to_crawl_queue) < max_urls:
                            to_crawl_queue.append((link, current_depth + 1))
        
        return results,failed_urls

    def _build_config(self, **kwargs) -> CrawlerRunConfig:
        """构建Crawl4AI配置"""
        prune_filter = PruningContentFilter(
            threshold=kwargs.get('threshold', 5),
            threshold_type="dynamic",
            min_word_threshold=kwargs.get('min_word_threshold', 5)
        )
        md_generator = DefaultMarkdownGenerator(content_filter=prune_filter)

        return CrawlerRunConfig(
            screenshot=False,
            magic=True,
            simulate_user=True,
            verbose=True,
            page_timeout=self.config.get('timeout', 30) * 1000,
            exclude_external_links=not kwargs.get('include_external', False),
            word_count_threshold=10,
            markdown_generator=md_generator,
            wait_until="networkidle",
            # scan_full_page=True,
            # remove_overlay_elements=True,
            delay_before_return_html=0.5,
            # mean_delay=0.5,
            # max_range=0.5,
        )

    async def process_pdf_file(self, url: str) -> CrawlResult:
        """处理PDF文件，应用钩子"""
        try:
            # 创建一个初始的CrawlResult实例
            initial_result = CrawlResult.create_success(
                url=url,
                content='PDF file pending processing',
                crawler_used="crawl4ai",
            )
            
            # 将实例而非类传递给钩子
            processed_pdf = await self.hooks.execute_hook("parse_pdf_file", url, initial_result)
            
            # 确保返回的是CrawlResult实例
            if isinstance(processed_pdf, CrawlResult):
                return processed_pdf
            else:
                # 如果返回的不是CrawlResult实例，则包装结果
                return CrawlResult.create_success(
                    url=url,
                    content=str(processed_pdf) if processed_pdf else 'PDF processing result',
                    crawler_used="crawl4ai",
                )
            
        except Exception as e:
            logger.error(f"处理PDF文件失败: {str(e)}")
            
            # 创建错误结果
            error_result = CrawlResult.create_failure(
                url=url,
                error_type="PDFProcessingError",
                error_code="PDF001",
                description=f"PDF处理失败: {str(e)}",
                crawler_used="crawl4ai"
            )
            
            # 通知钩子处理失败
            try:
                await self.hooks.execute_hook("parse_pdf_file_failure", url, error_result, e)
            except Exception as hook_error:
                logger.error(f"执行PDF处理失败钩子时出错: {str(hook_error)}")
            
            # 返回一个成功结果但包含错误信息
            return CrawlResult.create_success(
                url=url,
                content=f'PDF处理失败: {str(e)}',
                crawler_used="crawl4ai",
            )
